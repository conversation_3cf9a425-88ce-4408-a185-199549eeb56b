import { Hi, timer, env } from '@/libs/common'
import api from '@/api'


let duliActList = ['teamanswer', 'performancev3', 'performance', 'placeorder', 'signature', 'contract', ]
let oemDuluActList = ['teamanswer', 'placeorder', 'signature', 'contract', 'listlottery','listlotteryv3',  'piclottery', 'performancev3', 'performance', 'piclotteryv3','seglottery']
export default {
  name: 'watermark',
  namespaced: true,
  state: {
    independentObj: {},
  },
  getters: {
    isOpenWatermark(state, getters, rootState, rootGetters) {
      try {
        let activity = rootState.wall.activity
        let wall = rootState.wall.wall

        const upperName = Hi.String.firstToUpper(activity)
        let config =
          rootGetters[`${activity}/get${upperName}Config`] || rootGetters[`${activity}/getWall${activity}Config`]
        let actData = rootGetters[`${activity}/get${upperName}`] || rootGetters[`${activity}/getWall${activity}`]

        // 代理商
        if (wall.agentId) {
          console.log('代理商', wall.agentId, state.independentObj[activity])
          if (oemDuluActList.includes(activity)) {
            return state.independentObj[activity] === 'E'
          }
        } else {
          //hxc
          if (!duliActList.includes(activity)) return false
          if (wall.wallVersion === 'test') return true
          //如果活动级有数据，那以活动级配置为准
          if (config.independentLimit) {
            if (activity === 'signature') {
              if (actData.themeType === 'LONGLINQIANDAO') return config.longlinTheme === 'E' || config.independentLimit === 'E'
              return config.independentLimit === 'E'
            }
            if(activity === 'padsign') {
              return config.independentLimit !== 'Y' 
            }
            return config.independentLimit === 'E'
          } else {
            //如果活动级没有数据，那以用户级配置为准
            if (activity === 'signature') {
              if (actData.themeType === 'LONGLINQIANDAO') return state.independentObj.longlinTheme === 'E' || state.independentObj[activity] === 'E'
              return state.independentObj[activity] === 'E'
            }
            if(activity === 'padsign') {
              return config.independentLimit !== 'Y' 
            }
            return state.independentObj[activity] === 'E'
          }
        }

        return false
      } catch (error) {
        console.log(error)
      }
    },
    getOemDuliClosedActList(state, getters, rootState, rootGetters) {
      let wall = rootState.wall.wall
      let arr = []
      if (wall.agentId) {
        //如果是代理商
        oemDuluActList.forEach(item => {
          state.independentObj[item] === 'N' && arr.push(item)
        });
      } else {
        //如果是hxc的独立活动
        duliActList.forEach(item => {
          state.independentObj[item] === 'N' && arr.push(item)
        });
      }
      return arr
    }
  },
  actions: {
    async fetchIndependent({ commit, state, rootState, rootGetters }) {
      let data = await api.user.independent({})
      commit('setIndependent', data)
    }
  },
  mutations: {
    setIndependent(state, data) {
      state.independentObj = data
    }
  },
}
