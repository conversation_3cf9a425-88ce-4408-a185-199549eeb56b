<template>
  <div class="danmulottery-box" v-if="danmulottery && danmulottery.id">
    <div class="bgimg" :style="bgStyle"></div>
    <!-- <hi-bg-video v-else class="bgvideo" :autoplay="true" :src="danmulottery.webBgvideo"></hi-bg-video> -->
    <keep-alive :max="2">
      <component :themeData="themeData" :themeType="themeType" :lotteryId="danmulottery.id" :is="componentId" :key="componentId + danmulottery.id"></component>
    </keep-alive>
  </div>
  <!--抽奖无活动页面-->
  <hi-noact v-else type="弹幕抽奖" modelName="danmulottery"></hi-noact>
</template>
<script>
import api from '@/api' // 导入api接口对象集合
import { mapGetters, mapActions, mapMutations } from 'vuex'
import HiNoact from '../noact'
import HiReady from './ready'
import HiIng from './ing'
import HiFinish from './finish'
import { Hi } from '@/libs/common'
import { themeConfig } from './theme.js'
export default {
  name: 'danmulottery',
  components: {
    HiNoact,
    HiReady,
    HiFinish,
    HiIng,
  },
  data() {
    return {
      handler: {},
    }
  },
  computed: {
    ...mapGetters({
      danmulottery: 'danmulottery/getDanmulottery',
      getThemeObj: 'danmulottery/getThemeObj',
      themeType: 'danmulottery/getTheme',
    }),
    // 活动状态
    activityState() {
      //return 'IN' //调试代码todo
      return this.danmulottery.state || 'NOT_STARTED'
    },
    componentId() {
      let stateObj = {
        NOT_STARTED: 'HiReady',
        IN: 'HiIng',
        FINISH: 'HiFinish',
      }
      return stateObj[this.activityState]
    },
    bgStyle() {
      let bg = this.themeData.bgImg
      return {
        backgroundImage: `url(${bg})`,
      }
    },
    themeData() {
      let theme = this.getThemeObj
      return Object.assign({}, themeConfig[this.themeType], theme)
    }
  },
  methods: {
    ...mapActions({
      pollNormalMsg: 'msg/pollNormalMsg',
      closePollMsg: 'msg/closePollMsg',
    }),
    async actSwitch(drt = 'down') {
      if (this.isSwitchPost || !this.danmulottery.id) return
      this.isSwitchPost = true
      try {
        await api.danmulottery.switch({
          wallFlag: this.wallFlag,
          type: drt,
        })
      } catch (e) {
        this.$tip.msg(e.msg || '网络异常！', 2000)
        console.log(e)
      }
      this.isSwitchPost = false
    },
  },
  mounted() { },
  activated() {
    //绑定快捷键
    this.$bus.$on('control-up-down', this.actSwitch)
    this.pollNormalMsg()
  },
  deactivated() {
    this.$bus.$off('control-up-down', this.actSwitch)
    this.closePollMsg()
  },
}
</script>
<style scoped lang="stylus">
.danmulottery-box
  width 100%
  height 100%
  position relative
  .bgimg
  .bgvideo
    width 100%
    height 100%
    position absolute
    left 0
    top 0
    z-index 0
    background-size cover
    bapckground-repeat no-repeat
    bapckground-position center
</style>
