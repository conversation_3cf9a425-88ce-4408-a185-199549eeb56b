import api from './api'
export default {
  // 当前轮活动信息
  read: postData => api.fetchBaseData('/pro/hxc/web/profire/read.htm', postData),
  again: postData => api.fetchBaseData('/pro/hxc/web/profire/again.htm', postData),
  go: postData => api.fetchBaseData('/pro/hxc/web/profire/go.htm', postData),
  ranking: postData => api.fetchBaseData('/pro/hxc/web/profire/ranking.htm', postData),
  switch: postData => api.fetchBaseData('/pro/hxc/web/profire/switch.htm', postData),
  awardslist: postData => api.fetchBaseData('/pro/hxc/web/profireawards/list.htm', postData),
}
