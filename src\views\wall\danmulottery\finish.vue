<template>
  <div class="danmu-box">
    <danmulottery-rank :rankList="winnerList" @close="closeHandler" :themeData="themeData"></danmulottery-rank>
  </div>
</template>
<script>
import api from '@/api' // 导入api接口对象集合
import { mapGetters, mapActions, mapMutations } from 'vuex'
import DanmulotteryRank from './components/rank'
export default {
  name: 'danmulot',
  components: { DanmulotteryRank },
  props: {
    themeType: String,
    themeData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  data() {
    return {
      winnerList: [], // 当前轮中奖人列表
    }
  },
  computed: {
    ...mapGetters({
      danmulottery: 'danmulottery/getDanmulottery',
    }),
  },
  methods: {
    async getWinList() {
      // 查询当前轮中奖人
      try {
        const data = await this.fetchActRecordPage({
          where: { moduleId: this.danmulottery.id, module: 'danmulottery', uuid: this.danmulottery.batchNo },
          pageSize: 500,
        })
        // 关联弹幕信息
        let obj = {}
        let dataList = data.dataList
        if (dataList.length) {
          dataList.forEach(item => {
            const { msgId, wxUserId } = item
            obj[item.id] = Object.assign({}, { msgId, wxUserId })
          })
        }
        this.winnerList = dataList
        this.loading = false
      } catch (e) {
        console.log(e)
      }
    },
    async closeHandler() {
      await api.danmulottery.next({ danmulotteryId: this.danmulottery.id })
    },
    blindHandler() {
      this.handler = {
        'keyup-Space': this.closeHandler,
      }
      Object.keys(this.handler).forEach(key => this.$bus.$off(key))
      Object.keys(this.handler).forEach(key => this.$bus.$on(key, this.handler[key]))
    }
  },
  mounted() {
    this.blindHandler()
  },
  activated() {
    this.blindHandler()
    this.getWinList()
  },
  destroyed() {
    Object.keys(this.handler).forEach(key => this.$bus.$off(key))
  },
  deactivated() {
    Object.keys(this.handler).forEach(key => this.$bus.$off(key))
  }
}
</script>
<style scoped lang="stylus">
.danmu-box
  width 100%
  height 100%
  background-position center top
  background-size cover
  color #fff
.loading
  width 504px
  height 567px
  transform translateY(-4%)
</style>
