import api from './api'
export default {
  // 当前轮活动信息
  read: postData => api.fetchBaseData('/pro/hxc/web/prodiglett/read.htm', postData),
  // 答题开始
  go: postData => api.fetchBaseData('/pro/hxc/web/prodiglett/go.htm', postData),
  again: postData => api.fetchBaseData('/pro/hxc/web/prodiglett/again.htm', postData),
  // 切换活动
  switch: postData => api.fetchBaseData('/pro/hxc/web/prodiglett/switch.htm', postData),
  ranking: postData => api.fetchBaseData('/pro/hxc/web/prodiglett/ranking.htm', postData),
}
