<script>
import { mapGetters, mapMutations } from 'vuex'
import { Hi, timer, debounce, env } from '@/libs/common'
import api from '@/api'
import { rightBtnList, actList, centerObj } from './config'
const playguessStateObj = {
  ready: '开始',
  in: '正确',
  end: '下一组',
}
const voteStateObj = {
  start: '开始',
  cancel: '取消',
  pause: '暂停',
}
export default {
  name: 'bottom-control',
  data() {
    return {
      isClient: Hi.Client.isClient,
      isControlDown: false,
      controlTipsFlag: false,
      actList,
      rightBtnList,
      tempFlag: false, // 防止双击 双击只实现一次
      isFullscreen: false, // 是否全屏

      index: 1,
      translateY: 0,
      line: 2, // 左侧互动栏的行数

      tooltipStyle: {},
      tooltip: '',

      isPlayPauseAnimationName: true,
      clicked: false, // 默认未点击
      shortcutObj: {},
    }
  },
  computed: {
    ...mapGetters({
      dzoemHideRightList: 'oem/getDzOemHideRightList',
      dzoemHideActList: 'oem/getDzOemHideActList',
      showMoveQrcode: 'wall/isShowMoveQrcode',
      windowWidth: 'common/windowWidth',
      zoom: 'common/zoom',

      wall: 'wall/getWall',
      wallConfig: 'theme/getWallConfig',
      showHelp: 'wall/getShowHelp',
      tab: 'wall/getHelpTab',
      oemDuliClosedActList: 'watermark/getOemDuliClosedActList',

      wallmsgConfig: 'msg/getWallmsgConfig',
      msgForceStop: 'wallmsgControl/getForceStop',
      msgPlay: 'wallmsgControl/getPlay',
      msgFlip: 'wallmsgControl/getFlip',
      msgPlayReset: 'wallmsgControl/getPlayReset',

      wallsignConfig: 'sign/getWallsignConfig',
      wallthreedConfig: 'threed/getWallthreedConfig',
      wallcountdownConfig: 'countdown/getCountdownConfig',
      countdownPlaying: 'countdown/isPlaying',

      wallvoteConfig: 'vote/getWallvoteConfig',
      wallmarkConfig: 'mark/getWallmarkConfig',
      wallredpackConfig: 'redpack/getWallredpackConfig',
      walllotteryConfig: 'lottery/getWalllotteryConfig',
      danmulotteryConfig: 'danmulottery/getDanmulotteryConfig',
      piclotteryConfig: 'piclottery/getPiclotteryConfig',
      wallrewardConfig: 'reward/getWallrewardConfig',
      placeorderConfig: 'placeorder/getPlaceorderConfig',
      placeorder: 'placeorder/getPlaceorder',
      launchingceremonyConfig: 'launchingceremony/getLaunchingceremonyConfig',
      launchingceremony: 'launchingceremony/getLaunchingceremony',
      curMysterybox: 'mysterybox/getCurMysterybox',
      curPacketwall: 'packetwall/getCurPacketwall',
      signatureConfig: 'signature/getSignatureConfig',
      mapsignConfig: 'mapsign/getMapsignConfig',
      performanceConfig: 'performance/getPerformanceConfig',
      performance: 'performance/getPerformance',
      supperzzleConfig: 'supperzzle/getSupperzzleConfig',
      wishConfig: 'wish/getWishConfig',
      curWish: 'wish/getWish',
      signbookConfig: 'signbook/getSignbookConfig',

      wallpicConfig: 'pic/getWallpicConfig',
      picForceStop: 'wallpicControl/getForceStop',
      picPlay: 'wallpicControl/getPlay',
      picFlip: 'wallpicControl/getFlip',
      picPlayReset: 'wallpicControl/getPlayReset',

      wallquestionConfig: 'question/getWallquestionConfig',
      qLook: 'question/getQLook',

      wallvoicepackConfig: 'voicepack/getWallvoicepackConfig',
      wallvote: 'vote/getWallvote',
      voteState: 'vote/getVoteState',
      mark: 'mark/getWallmark',

      wallanswerraceConfig: 'answerrace/getWallanswerraceConfig',
      wallanswerrace: 'answerrace/getWallanswerrace',
      ingState: 'answerrace/getIngState',

      wallshootConfig: 'shoot/getWallshootConfig',
      walltugwarConfig: 'tugwar/getWalltugwarConfig',

      wallplayguessConfig: 'playguess/getWallplayguessConfig',
      playguessState: 'playguess/playguessState',

      wallguestConfig: 'guest/getWallguestConfig',
      wallropepackConfig: 'ropepack/getWallropepackConfig',
      walltopicConfig: 'topic/getWalltopicConfig',
      wallcontractConfig: 'contract/getWallcontractConfig',

      activity: 'wall/getActivity',
      // 嘉宾墙
      isShowGuestUpDown: 'guest/isShowGuestUpDown',
      curActHasMusic: 'music/curActHasMusic',
      teamanswerConfig: 'teamanswer/getTeamanswerConfig',
      getControl: 'teamanswer/getControl',
      wheelsurfConfig: 'wheelsurf/getWheelsurfConfig',
      ninegridsConfig: 'ninegrids/getNinegridsConfig',
      mysteryboxConfig: 'mysterybox/getMysteryboxConfig',
      packetwallConfig: 'packetwall/getPacketwallConfig',
      listlotteryConfig: 'listlottery/getListlotteryConfig',
      photolotteryConfig: 'photolottery/getPhotolotteryConfig',
      shakev3Config: 'shakev3/getShakev3Config',
      shakev3Name: 'shakev3/getThemeName',
      moneyv3Config: 'moneyv3/getMoneyv3Config',
      diglettv3Config: 'diglettv3/getDiglettv3Config',
      goldcoinv3Config: 'goldcoinv3/getGoldcoinv3Config',
      firev3Config: 'firev3/getFirev3Config',
      firev3Name: 'firev3/getThemeName',
      goldcoinv3Name: 'goldcoinv3/getThemeName',
      performancev3Config: 'performancev3/getPerformancev3Config',
      lotteryv3Config: 'lotteryv3/getLotteryv3Config',
      listlotteryv3Config: 'listlotteryv3/getListlotteryv3Config',
      piclotteryv3Config: 'piclotteryv3/getPiclotteryv3Config',
      seglotteryConfig: 'seglottery/getSeglotteryConfig',
      answerracev3Config: 'answerracev3/getAnswerracev3Config',
    }),
    controlClass() {
      return !this.isControlDown || this.showHelp ? 'up' : 'down'
    },
    isOpen() {
      //本地开发环境无条件放行，方便开发
      if (env.NODE_ENV === 'development' && !env.isPro) {
        return id => {
          if (this.dzoemHideActList.includes(id)) return false
          if (this.oemDuliClosedActList.includes(id)) return false
          // console.log('开发环境，无条件放行',this.oemDuliClosedActList,id)
          let obj = this['wall' + id + 'Config'] || this[id + 'Config']
          if (obj && obj.openState === 'N') return false
          return true
        }
      }
      return id => {
        if (this.dzoemHideActList.includes(id)) return false
        if (this.oemDuliClosedActList.includes(id)) return false
        let obj = this['wall' + id + 'Config'] || this[id + 'Config']
        return obj && obj.openState === 'Y'
      }
    },
    openActArr() {
      let arr = []
      this.actList.forEach(item => {
        if (this.isOpen(item.id)) {
          arr.push(item)
        }
      })
      return arr
    },
    isDanmuOpen() {
      let obj = this.rightBtnList.find(item => item.id === 'danmu')
      return obj ? !obj.isBan : false
    },
    isMusicPlay() {
      let obj = this.rightBtnList.find(item => item.id === 'music')
      return obj ? !obj.isBan : false
    },
    isZhao() {
      // 默认禁止
      let obj = this.rightBtnList.find(item => item.id === 'zhao')
      return obj ? !obj.isBan : false
    },
    isPlay() {
      // return !this.forceStop && this.play && !this.flip && (this.activity === 'msg' || this.activity === 'pic');
      return (
        !this[`${this.activity}ForceStop`] &&
        !this[`${this.activity}Flip`] &&
        this[`${this.activity}Play`] &&
        (this.activity === 'msg' || this.activity === 'pic')
      )
    },
    playPauseLoadStyle() {
      let time = this.wallmsgConfig.turnTime || 4
      time < 3 ? (time = 3) : time++
      return {
        animationDuration: time + 's',
        animationPlayState: this.isPlay ? 'running' : 'paused',
      }
    },
    // 当前互动下，中间的操作按钮对象
    curCenter() {
      const arr = centerObj[this.activity]
      let obj = {}
      if (!arr || !arr.length) return obj
      let brr = Hi.Object.copy(arr)
      brr.forEach(item => (obj[item.id] = item))
      switch (this.activity) {
        case 'msg':
          obj.space.img = !this.msgPlay ? 'play' : 'pause'
          obj.space.name = !this.msgPlay ? '开始' : '暂停'
          break
        case 'pic':
          obj.space.img = !this.picPlay ? 'play' : 'pause'
          obj.space.name = !this.picPlay ? '开始' : '暂停'
          break
        case 'placeorder':
          obj.space.img = this.placeorder.state === 'IN' ? 'pause' : 'play'
          obj.space.name = this.placeorder.state === 'IN' ? '暂停' : '开始'
          break
        case 'performance':
          obj.space.img = this.performance.state === 'IN' ? 'pause' : 'play'
          obj.space.name = this.performance.state === 'IN' ? '暂停' : '开始'
          break
        case 'mark':
          obj.space.img = this.mark.state === 'IN' ? 'pause' : 'play'
          obj.space.name = this.mark.state === 'IN' ? '暂停' : '开始'
          break
        case 'mysterybox':
          if (this.curMysterybox) {
            if (['NOT_STARTED', 'PAUSE'].includes(this.curMysterybox.activityState)) {
              obj.space.img = 'play'
              obj.space.name = '开始'
            }
            if (['IN'].includes(this.curMysterybox.activityState)) {
              obj.space.img = 'pause'
              obj.space.name = '暂停'
            }
            if (['FINISH'].includes(this.curMysterybox.activityState)) {
              obj.space.img = 'end'
              obj.space.name = '已结束'
            }
          }
          break
        case 'packetwall':
          if (this.curPacketwall) {
            if (['NOT_STARTED', 'PAUSE'].includes(this.curPacketwall.activityState)) {
              obj.space.img = 'play'
              obj.space.name = '开始'
            }
            if (['IN'].includes(this.curPacketwall.activityState)) {
              obj.space.img = 'pause'
              obj.space.name = '暂停'
            }
            if (['FINISH'].includes(this.curPacketwall.activityState)) {
              obj.space.img = 'end'
              obj.space.name = '已结束'
            }
          }
          break
        case 'wish':
          if (this.curWish) {
            if (['PAUSE'].includes(this.curWish.state)) {
              obj.space.img = 'play'
              obj.space.name = '开始'
            }
            if (['NORMAL'].includes(this.curWish.state)) {
              obj.space.img = 'pause'
              obj.space.name = '暂停'
            }
          }
          break
        case 'question':
          obj.space.img = this.qLook ? 'eyeclose' : 'eye'
          obj.space.name = this.qLook ? '关闭' : '查看'
          break
        case 'guest':
          obj.space.name = this.isShowGuestUpDown ? '缩小' : '放大'
          obj.space.img = this.isShowGuestUpDown ? 'tosmall' : 'tobig'
          if (!this.isShowGuestUpDown) {
            obj.up.hidden = true
            obj.down.hidden = true
          } else {
            obj.up.hidden = false
            obj.down.hidden = false
          }
          break
        case 'answerrace':
          if (this.ingState === 'done') {
            obj.space = {
              id: 'space',
              name: this.clicked ? '关闭注释' : '查看注释',
              img: 'zhushi',
              key: 'keyup-Space',
              tooltip: '快捷键(空格)',
            }
          } else {
            delete obj.space
          }
          if (this.wallanswerrace && this.wallanswerrace.state !== 'IN') {
            obj.left.hidden = true
            obj.right.hidden = true
          } else {
            obj.left.hidden = false
            obj.right.hidden = false
          }
          break
        case 'playguess':
          obj.space.img = 'play'
          obj.space.name = playguessStateObj[this.playguessState]
          // obj.left.name = '上一个'
          if (this.playguessState === 'ready') {
            obj.left.hidden = false
            obj.right.hidden = false
          } else if (this.playguessState === 'in') {
            obj.up.name = '跳过'
            obj.up.hidden = false
            obj.down.hidden = true
            obj.left.hidden = true
            obj.right.hidden = true
          } else if (this.playguessState === 'end') {
            obj.up.hidden = false
            obj.down.hidden = false
            obj.left.hidden = true
            obj.right.hidden = true
          }
          break
        case 'vote':
          obj.space.img = this.voteState === 'start' ? 'play' : this.voteState
          obj.space.name = voteStateObj[this.voteState]
          if (this.wallvote && this.wallvote.screenStyle !== 'single') {
            obj.left.hidden = true
            obj.right.hidden = true
          } else {
            obj.left.hidden = false
            obj.right.hidden = false
          }
          break
        case 'contract':
          obj.space.img = 'save'
          obj.space.name = '保存'
          break
        case 'countdown':
          obj.space.img = !this.countdownPlaying ? 'play' : 'pause'
          obj.space.name = !this.countdownPlaying ? '开始' : '暂停'
          break
        case 'launchingceremony':
          if (this.launchingceremony.state === 'NOT_STARTED') {
            obj.space.img = 'play'
            obj.space.name = '开始'
          }
          if (this.launchingceremony.state === 'FINISH') {
            obj.space.img = 'reset'
            obj.space.name = '重置'
          }
          if (['IN', 'COHESION'].includes(this.launchingceremony.state)) {
            obj.space.img = 'success'
            obj.space.name = '聚力成功'
          }
        default:
          break
      }
      return obj
    },
    //团队答题专属
    centerBtnList() {
      let { hideArr, change } = this.getControl
      let arr = Hi.Object.copy(centerObj[this.activity])
      if (hideArr && hideArr.length) {
        arr = arr.filter(item => !hideArr.includes(item.id))
      }
      if (Object.keys(change).length) {
        arr = arr.map(item => {
          if (change[item.id]) {
            Object.assign(item, change[item.id])
          }
          return item
        })
      }
      return arr
    },
  },
  watch: {
    isDanmuOpen(val) {
      this.$bus.$emit('isDnmuOpen', val)
    },
    isMusicPlay: {
      immediate: true,
      handler(val) {
        this.$bus.$emit('isMusicPlay', val)
        this.musicPlaySwitch(val)
      },
    },
    async activity(v) {
      // 初始化按钮点击状态
      this.clicked = false
      // 互动切换时绑定解绑快捷键
      this.bindKey(v)
      // 是否自动切屏
      if (this.isZhao) {
        if (
          ![
            'redpack',
            'voicepack',
            'vote',
            'reward',
            'answerrace',
            'shoot',
            'tugwar',
            'ropepack',
            'msg',
            'topic',
            'placeorder',
            'signature',
            'launchingceremony',
            'mapsign',
            'supperzzle',
            'performance',
            'teamanswer',
            'wish',
            'signbook',
            'shakev3',
            'moneyv3',
            'diglettv3',
            'goldcoinv3',
            'firev3',
            'performancev3',
            'answerracev3',
          ].includes(v)
        )
          return
        let obj = {}
        this.actList.forEach(item => (obj[item.id] = item.name))
        await api.common.sendCmd({
          module: 'wall',
          cmd: 'pro_ready',
          bizData: {
            title: obj[v] + '游戏',
            type: v,
            source: 'pcwall',
          },
        })
      }
      // 对应活动是否有声音
      const hasMusic = [
        'countdown',
        'voicepack',
        'shoot',
        'tugwar',
        'ropepack',
        'placeorder',
        'launchingceremony',
        'leglottery',
      ].includes(v)
      if (hasMusic || this.curActHasMusic || v.includes('v3')) {
        let obj = this.rightBtnList.find(item => item.id === 'music')
        obj && (obj.tooltip = '屏幕音效开关,快捷键[shift+Y]')
      } else {
        let obj = this.rightBtnList.find(item => item.id === 'music')
        obj && (obj.tooltip = '屏幕音效开关，当前互动无音效')
      }
    },
    // 计时重置
    async msgPlayReset() {
      this.isPlayPauseAnimationName = false
      await this.$nextTick()
      await timer(500)
      this.isPlayPauseAnimationName = true
    },
    // 计时重置
    async picPlayReset() {
      this.isPlayPauseAnimationName = false
      await this.$nextTick()
      await timer(500)
      this.isPlayPauseAnimationName = true
    },
  },
  methods: {
    ...mapMutations({
      setCurQrcodeShow: 'wall/setCurQrcodeShow',
      setActivity: 'wall/setActivity',
      setShowHelp: 'wall/setShowHelp',
      setmsgPlay: 'wallmsgControl/setPlay',
      setmsgPlayReset: 'wallmsgControl/setPlayReset',
      setpicPlay: 'wallpicControl/setPlay',
      setpicPlayReset: 'wallpicControl/setPlayReset',
      setCountdownPlaying: 'countdown/setPlaying',
      setVoteState: 'vote/setVoteState',
      musicPlaySwitch: 'music/musicPlaySwitch',
    }),
    rightBtnStyle(item) {
      if (item.id === 'moveqrcode') {
        return { filter: !this.showMoveQrcode ? 'brightness(1.4)' : 'none' }
      }
      return { filter: item.isBan ? 'brightness(1.4)' : 'none' }
    },
    rightHandler(id, index) {
      if (this.tempFlag) return
      this.tempFlag = true
      if (id === 'moveqrcode') {
        this.setCurQrcodeShow(!this.showMoveQrcode)
      }
      if (id === 'fullscreen') {
        this.isFullscreen = !this.isFullscreen
        if (this.isFullscreen) {
          this.fullScreen()
        } else {
          this.escControl()
        }
      } else if (id === 'help') {
        // 显示帮助弹窗
        this.setShowHelp(!this.showHelp)
      } else {
        this.rightBtnList[index].isBan = !this.rightBtnList[index].isBan
      }
      setTimeout(() => {
        this.tempFlag = false
      }, 500)
    },
    escControl() {
      var de = document
      if (de.exitFullscreen) {
        de.exitFullscreen()
      } else if (de.mozCancelFullScreen) {
        de.mozCancelFullScreen()
      } else if (de.webkitCancelFullScreen) {
        de.webkitCancelFullScreen()
      }
    },
    fullScreen() {
      var de = document.documentElement
      if (de.requestFullscreen) {
        de.requestFullscreen()
      } else if (de.mozRequestFullScreen) {
        de.mozRequestFullScreen()
      } else if (de.webkitRequestFullScreen) {
        de.webkitRequestFullScreen()
      } else if (typeof window.ActiveXObject != 'undefined') {
        var wscript = new ActiveXObject('WScript.Shell')
        if (wscript) {
          wscript.SendKeys('{F11}')
        }
      }
    },
    activityChange(id) {
      // 检测是否开启
      if (this.isOpen(id)) this.setActivity(id)
    },
    async dealScroll(dir) {
      await this.$nextTick()
      const h = this.$refs.controlCont.offsetHeight,
        scrollH = this.$refs.scroll.offsetHeight
      this.line = Math.round(scrollH / h)
      if (dir === 'resize') {
        // 窗口缩放后，重置到第一行
        this.index = 1
        this.translateY = 0
        return
      }
      if (!dir) return
      if (dir === 'up') {
        if (this.index === 1) {
          this.translateY = -h * (this.line - 1)
          this.index = this.line
        } else {
          this.translateY += h
          this.index--
        }
      } else {
        if (this.index < this.line) {
          this.translateY -= h
          this.index++
        } else {
          this.translateY = 0
          this.index = 1
        }
      }
    },
    async showTooltipTip(e, tooltip, realW) {
      if (Hi.Type.isObject(tooltip)) {
        let obj = this.shortcutObj[tooltip.id]
        if (obj) {
          this.tooltip = `${tooltip.name}，快捷键[${obj.shortcut.replace('Key', '').replace('Digit', '')}]`
        } else {
          this.tooltip = tooltip.name
        }
      } else {
        this.tooltip = tooltip
      }
      await this.$nextTick()
      const { left, width } = e.target.getBoundingClientRect()
      if (!this.$refs.tooltip) return
      const w = this.$refs.tooltip.getBoundingClientRect().width,
        tempL = left + Math.floor(width - w) / 2,
        offR = document.body.clientWidth / this.zoom - w
      let tooltipLeft = tempL
      if (tempL < 0) {
        tooltipLeft = 5
        // realW = w + tempL
      } else if (tempL > offR) {
        // tooltipLeft = offR - 5
        // realW = w - (tempL - offR)
      }
      this.tooltipStyle = {
        left: tooltipLeft + 'px',
        // width:realW + 'px'
      }
      if (w) this.tooltipStyle.width = realW + 'px'
    },
    //互动名称
    activeName(item) {
      if (!['launchingceremony', 'shakev3', 'firev3', 'goldcoinv3'].includes(item.id)) {
        return this.getI18nActName(item)
      }
      if (item.id === 'launchingceremony') {
        return this.launchingceremony.name || this.getI18nActName(item)
      }
      if (item.id === 'firev3') {
        return this.firev3Name || this.getI18nActName(item)
      }
      if (item.id === 'goldcoinv3') {
        return this.goldcoinv3Name || this.getI18nActName(item)
      }
      if (item.id === 'shakev3') {
        return this.shakev3Name || this.getI18nActName(item)
      }
    },
    playPauseToggle() {
      let playHandler = this[`set${this.activity}Play`]
      if (playHandler) {
        playHandler(!this[`${this.activity}Play`])
      }
    },
    playControlEnd() {
      if (this.activity === 'msg') {
        this.toggleControl.call(this, 'nextPage')
      }
    },
    toggleControl(name) {
      const playResetHandler = this[`set${this.activity}PlayReset`]
      if (playResetHandler) {
        playResetHandler()
      }
      this.$bus.$emit(name)
    },
    leftRight(type = 'right') {
      if (['msg', 'pic'].includes(this.activity)) {
        this.toggleControl(type)
      } else {
        //每次更新设计器新互动，很可能需要在这里增加哦，别忘了
        const arr = ['lotteryv3', 'listlotteryv3', 'piclotteryv3', 'answerracev3']
        if (arr.includes(this.activity)) {
          //特定互动设计器互动将事件传递下去
          let keyObj = {
            left: { key: 'ArrowLeft', code: 37, },
            right: { key: 'ArrowRight', code: 39, },
          }
          const createEvent = new KeyboardEvent('keyup', keyObj[type])
          window.dispatchEvent(createEvent)
        }
        this.$bus.$emit('control-left-right', type)
      }
    },
    // 上一页，下一页
    upDown(type) {
      console.log('upDown', type)
      if (['msg', 'pic'].includes(this.activity)) {
        this.toggleControl(type === 'up' ? 'prevPage' : 'nextPage')
      } else {
        this.$bus.$emit('control-up-down', type)
      }
    },
    space() {
      if (['msg', 'pic'].includes(this.activity)) {
        this.playPauseToggle()
      } else if (this.activity === 'countdown') {
        if (!this.countdownPlaying) {
          this.setCountdownPlaying(true)
        }
      } else {
        // 各种互动开始处理
        this.$bus.$emit('keyup-Space')
      }
      this.clicked = !this.clicked
    },
    // 互动切换时，中间操作按钮的快捷键解绑
    bindKey(v) {
      // 先解绑再绑定
      const eventHandler = {
        'keyup-ArrowLeft': this.leftRight.bind(this, ['msg', 'pic'].includes(this.activity) ? 'firstPage' : 'left'),
        'keyup-ArrowUp': this.upDown.bind(this, 'up'),
        'keyup-ArrowDown': this.upDown.bind(this, 'down'),
        'keyup-ArrowRight': this.leftRight.bind(this, ['msg', 'pic'].includes(this.activity) ? 'lastPage' : 'right'),
        'keyup-Space': this.space.bind(this),
      }
      // 先解绑再绑定
      Object.keys(eventHandler).forEach(key => {
        this.$bus.$off(key)
      })
      let arr = centerObj[v]
      if (arr && arr.length) {
        arr.forEach(item => {
          if (item.key !== 'keyup-Space') {
            this.$bus.$on(item.key, eventHandler[item.key])
          } else {
            if (['msg', 'pic', 'countdown'].includes(v)) {
              this.$bus.$on(item.key, eventHandler[item.key])
            }
          }
        })
      }
    },
    resetRouter() {
      // 需求更改，默认互动为消息墙
      let activity = this.$route.query.activity
      if (!this.isOpen(activity)) {
        if (!this.isOpen('msg')) {
          for (let i = 0, len = this.openActArr.length; i < len; i++) {
            let item = this.openActArr[i]
            if (item.id !== 'danmu') {
              activity = item.id
              break
            }
          }
        } else {
          activity = 'msg'
        }
      }
      this.setActivity(activity)
    },
    centerClick(item) {
      if (['up', 'down'].includes(item.id)) {
        this.upDown(item.id)
        return
      }
      if (['left', 'right'].includes(item.id)) {
        this.leftRight(item.id)
        return
      }
      let eName = `${item.key}`
      if (item.key.includes('shift')) {
        eName = `keyup-shift_${item.key}`
      }
      this.$bus.$emit(eName)
    },
    async fetchShortcutList() {
      try {
        let data = await api.user.usershortcutList()
        let obj = {}
        data.forEach(item => {
          if (!item.shortcut) return
          let eName = `keyup-${item.shortcut}`
          this.$bus.$on(eName, () => {
            this.activityChange(item.model)
          })
          obj[item.model] = item
        })
        this.shortcutObj = obj
      } catch (error) {
        console.error(error)
      }
    },
    dealSpecialOemHide() {
      // 隐藏右侧按钮
      this.rightBtnList = this.rightBtnList.filter(item => !this.dzoemHideRightList.includes(item.id))
    },
    getI18nActName(item) {
      if (!this.$i18n.te(`control.actName.${item.id}`)) {
        return item.name
      }
      return this.$t(`control.actName.${item.id}`)
    },
    getActI18nTips(item) {
      let obj = this.shortcutObj[item.id]
      const name = this.getI18nActName(item)
      const kuaijiejian = this.$t('control.keyTips')
      if (obj) {
        return `${name}，${kuaijiejian}[${obj.shortcut.replace('Key', '').replace('Digit', '')}]`
      }
      return name
    },
    getCenterI18nTips(item) {
      let reg = /\((.+)\)/
      let tooltip = ''
      if (Hi.Type.isString(item)) {
        tooltip = item
      } else {
        tooltip = item.tooltip
      }
      let result = tooltip.match(reg)
      return this.$t('control.keyTips') + (result ? result[0] : '')
    },
  },
  mounted() {
    this.dealSpecialOemHide()
    this.fetchShortcutList()
    this.resetRouter()
    this.$bus.$on('resetRouter', this.resetRouter.bind(this))
    // 绑定左侧互动快捷键
    actList.forEach(item => {
      if (!item.keyCode) return
      let eName = `keyup-${item.keyCode}`
      if (item.keyName.includes('shift')) {
        eName = `keyup-shift_${item.keyCode}`
      }
      if (item.keyName.includes('ctrl')) {
        eName = `keyup-ctrl_${item.keyCode}`
      }
      this.$bus.$on(eName, () => {
        this.activityChange.call(this, item.id)
      })
    })
    // 绑定右侧常驻操作按钮快捷键
    this.$bus.$on('keyup-KeyD', () => {
      let obj = this.rightBtnList.find(item => item.id === 'danmu')
      obj && (obj.isBan = !obj.isBan)
    })
    this.$bus.$on(`keyup-shift_KeyY`, () => {
      let obj = this.rightBtnList.find(item => item.id === 'music')
      obj && (obj.isBan = !obj.isBan)
    })
    // 窗口缩放
    this.$bus.$on('resize', debounce(this.dealScroll.bind(this, 'resize'), 200))
    // 初始化左侧栏的行数
    this.dealScroll()
  },
}
</script>
<template>
  <div :class="['control-box', controlClass]" :style="{ zIndex: showHelp && tab === 'shortcut' ? 501 : 498 }" v-clientmouse @mouseenter="isControlDown = false" @mouseleave="
      v => {
        ;(isControlDown = true), (controlTipsFlag = true)
      }
    " @touchstart="isControlDown = false">
    <div class="seat-size">
      <template v-if="!controlTipsFlag">
        <div class="ctrlbartips">
          <img src="~@/assets/wall/pop.png" />
        </div>
        <span class="control-arrow" :style="{ backgroundImage: `url(${require('@/assets/wall/contr-arrow.png')})` }" @click=";(isControlDown = true), (controlTipsFlag = true)"></span>
      </template>
    </div>
    <!-- 底部控制台内容 -->
    <div class="cont flex flex-a-c flex-j-c" ref="controlCont">
      <div class="l">
        <div class="scroll flex flex-a-c" ref="scroll" :style="{ transform: `translate3d(0,${translateY}px,0)` }">
          <div class="item" v-for="item in openActArr" :key="item.id" :style="{ color: item.color }" :id="item.id" @click="activityChange(item.id)" @mouseover="showTooltipTip($event, getActI18nTips(item))" @mouseout="tooltip = ''">
            <icon scale="1.75" :name="item.icon"></icon>
            <p class="name" :class="[item.id === activity ? 'on' : '']">{{ activeName(item) }}</p>
          </div>
        </div>
        <div class="switch-page" v-if="line > 1">
          <div class="top" @click="dealScroll('up')">
            <img src="./img/up1.png" alt="上一页" />
          </div>
          <div class="bottom" @click="dealScroll('down')">
            <img src="./img/down1.png" alt="下一页" />
          </div>
        </div>
      </div>
      <div class="c flex flex-a-c flex-j-c">
        <template v-if="activity === 'teamanswer'">
          <div v-for="item in centerBtnList" :key="item.id" class="item" @click="centerClick(item)" @mouseover="showTooltipTip($event, item.tooltip)" @mouseout="tooltip = ''">
            <img class="img" :src="require(`./img/${item.img}.png`)" :alt="item.name" />
            <p class="name">{{ item.name }}</p>
          </div>
        </template>
        <template v-else>
          <template v-if="(activity === 'msg' && this.wallConfig.msgShow !== 'N') || activity !== 'msg'">
            <div v-if="curCenter.first" @click="leftRight('firstPage')" class="item" @mouseover="showTooltipTip($event, getCenterI18nTips(curCenter.first.tooltip))" @mouseout="tooltip = ''">
              <img class="img" src="./img/first.png" :alt="curCenter.first.name" />
              <p class="name">{{$t(`control.center.${curCenter.first.name}`) }}</p>
            </div>
            <div v-if="curCenter.up && !curCenter.up.hidden" @click="upDown('up')" class="item" @mouseover="showTooltipTip($event,getCenterI18nTips(curCenter.up.tooltip))" @mouseout="tooltip = ''">
              <img class="img" src="./img/up.png" :alt="curCenter.up.name" />
              <p class="name">{{ $t(`control.center.${curCenter.up.name}`)}}</p>
            </div>
            <div v-if="curCenter.left && !curCenter.left.hidden" @click="leftRight('left')" class="item" @mouseover="showTooltipTip($event, getCenterI18nTips(curCenter.left.tooltip))" @mouseout="tooltip = ''">
              <img class="img" :src="require(`./img/${curCenter.left.img || 'left'}.png`)" :alt="curCenter.left.name" />
              <p class="name">{{ $t(`control.center.${curCenter.left.name}`) }}</p>
            </div>
            <div v-if="curCenter.space" @mouseup.stop="space()" class="item" @mouseover="showTooltipTip($event, getCenterI18nTips(curCenter.space.tooltip))" @mouseout="tooltip = ''">
              <img class="img" :src="require(`./img/${curCenter.space.img || 'play'}.png`)" :alt="curCenter.space.name" />
              <p class="name">{{ $t(`control.center.${curCenter.space.name}`)}}</p>
            </div>
          </template>
          <div v-if="activity === 'contract'" @click="$bus.$emit('clearContract')" class="item" @mouseover="showTooltipTip($event, '快捷键(0)')" @mouseout="tooltip = ''">
            <img class="img" src="./img/resign.png" alt="重签" />
            <p class="name">重签</p>
          </div>
          <div v-if="curCenter.right && !curCenter.right.hidden" @click="leftRight('right')" class="item" @mouseover="showTooltipTip($event, getCenterI18nTips(curCenter.right.tooltip))" @mouseout="tooltip = ''">
            <img class="img" :src="require(`./img/${curCenter.right.img || 'right'}.png`)" :alt="curCenter.right.name" />
            <p class="name">{{ curCenter.right.name }}</p>
          </div>
          <div v-if="curCenter.down && !curCenter.down.hidden" @click="upDown('down')" class="item" @mouseover="showTooltipTip($event,getCenterI18nTips( curCenter.down.tooltip))" @mouseout="tooltip = ''">
            <img class="img" src="./img/down.png" :alt="curCenter.down.name" />
            <p class="name">{{$t(`control.center.${curCenter.down.name}`) }}</p>
          </div>
          <div v-if="curCenter.last" @click="leftRight('lastPage')" class="item" @mouseover="showTooltipTip($event, getCenterI18nTips(curCenter.last.tooltip))" @mouseout="tooltip = ''">
            <img class="img" src="./img/last.png" :alt="curCenter.last.name" />
            <p class="name">{{ $t(`control.center.${curCenter.last.name}`) }}</p>
          </div>
        </template>
      </div>
      <div class="r flex flex-a-c flex-j-c">
        <div class="item" v-for="(item, index) in rightBtnList" :key="item.id" @click="rightHandler(item.id, index)" @mouseover="showTooltipTip($event, item.tooltip)" @mouseout="tooltip = ''">
          <img class="img" :src="require(`./img/${item.id}.png`)" :style="rightBtnStyle(item)" :alt="item.name" />
          <icon v-if="item.id === 'moveqrcode' && !showMoveQrcode" name="wall_ban" class="ban"></icon>
          <icon v-else-if="item.id !== 'moveqrcode' && item.isBan" name="wall_ban" class="ban"></icon>
          <p class="name">{{ $t(`control.right.${item.id}`)}}</p>
        </div>
      </div>
      <div class="down-btn flex flex-a-c">
        <div class="item" @click="setShowHelp(!showHelp)">
          <img class="img" src="./img/help.png" />
          <p class="name">{{ $t('control.rightTips.help') }}</p>
        </div>
        <div class="item" @mouseover="showTooltipTip($event,$t('control.rightTips.fold'), 60)" @mouseout="tooltip = ''" :style="{ marginLeft: '-10px' }">
          <img src="./img/down-btn.png" alt="向下" />
        </div>
      </div>
    </div>
    <!-- tooltip -->
    <transition name="fade">
      <div v-if="tooltip" ref="tooltip" class="tooltip" :style="tooltipStyle">
        {{ tooltip }}
        <div class="arrow"></div>
      </div>
    </transition>
    <div v-if="['msg', 'pic'].includes(activity)">
      <span :style="playPauseLoadStyle" :class="{ 'play-pause-load': true, 'play-pause-animation-name': isPlayPauseAnimationName }" @animationiteration="playControlEnd">
        <icon name="control_load" class="control-icon" scale="2.5"></icon>
      </span>
    </div>
  </div>
</template>
<style lang="stylus" scoped>
.control-box
  position absolute
  z-index 501
  width 100%
  height 65px
  transition 0.5s
  user-select none
  backface-visibility hidden
  transform-style preserve-3d
.control-box.down
.control-box.client
  bottom -40px
.control-box.up
  bottom 25px
.seat-size
  position relative
  width 100%
  height 25px
.ctrlbartips
  position absolute
  width 194px
  height 77px
  top -70px
  right -5px
  animation shake 2s linear infinite
img
  display inline-block
  width 100%
  height 100%
@keyframes shake
  0%
    transform translateY(0px)
  10%
    transform translateY(-5px)
  15%
    transform translateY(0px)
  20%
    transform translateY(-5px)
  25%
    transform translateY(0px)
  30%
    transform translateY(-5px)
  35%
    transform translateY(0px)
  40%
    transform translateY(-5px)
  45%
    transform translateY(0px)
  50%
    transform translateY(-5px)
  67%
    transform translateY(0px)
  84%
    transform translateY(-5px)
  100%
    transform translateY(0px)
.cont
  width 100%
  height 100%
  padding-left 20px
  background-color #fff
  box-sizing border-box
  .l
  .c
  .r
    height 46px
    border-right 1px solid #e6e6e6
  .down-btn
    width 130px
    img
      width 24px
      margin 0 10px
  .r
    width 326px
    .item
      margin-right 10px
  .c
    width 320px
  .item
    flex-shrink 0
    margin 0 12px
    position relative
    cursor pointer
    display flex
    align-items center
    justify-content center
    flex-direction column
    transition opacity 0.2s
    .img
      width 28px
    .name
      white-space nowrap
      margin 0
      font-size 12px
      margin-top 5px
      color #38444c
      backface-visibility hidden
    .on
      color inherit
    .ban
      position absolute
      top 50%
      left 50%
      transform translate(-50%, -100%)
      color #ff3d3d
  .item svg
  .item svg path
    pointer-events none
  .item:hover
    opacity 0.8
  .l
    // width max-content
    position relative
    flex 1
    overflow hidden
    height 100%
    .item
      margin 0 7px
      width 48px
      height 65px
      text-align center
    .item:hover
      .name
        color inherit
.tooltip
  position fixed
  z-index 2106
  bottom 40px
  padding 10px
  background rgba(0, 0, 0, 0.8)
  border-radius 4px
  color #fff
  font-size 12px
  line-height 1.2
  max-width 150px
  width max-content
  word-wrap break-word
  transform-origin center bottom
  pointer-events none
  .arrow
    position absolute
    display block
    width 0
    height 0
    border-color transparent
    border-style solid
    border-width 6px
    left calc(50% - 6px)
    bottom -5px
    border-top-color rgba(0, 0, 0, 0.8)
    border-bottom-width 0
.scroll
  flex-wrap wrap
  flex-direction row
  width calc(100% - 30px)
  position relative
  top 0
  left 0
  transition transform 0.3s
  transform-style preserve-3d
.switch-page
  position absolute
  top 8.5px
  right 15px
  height 46px
  div
    cursor pointer
    padding 2px
    img
      width auto
      height 20px
.play-pause-animation-name
  position absolute
  bottom -100px
  animation-name play-pause-load
.play-pause-load
  animation-play-state paused
  animation-timing-function linear
  animation-iteration-count infinite
  transform-origin center
@keyframes play-pause-load
  from
    transform rotate(0deg)
  to
    transform rotate(360deg)
.border
  border 1px solid #54a4ff
  border-radius 10px
  box-shadow 0 0 5px rgba(0, 0, 0, 0.5)
</style>
