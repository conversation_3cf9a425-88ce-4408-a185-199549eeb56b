<template>
  <div class="danmu-box" v-if="isInitfinish" :style="danmuBoxStyle"></div>
</template>
<script>
import randomColor from 'randomcolor'
import { mapState, mapGetters } from 'vuex'
import { Hi, timer } from '@/libs/common'
import { relationUserMixin } from '@/libs/mixins'
const msgAvatarShade = require('./img/avatarShade.png')
function rgb() {
  //rgb颜色随机
  var r = Math.floor(Math.random() * 256)
  var g = Math.floor(Math.random() * 256)
  var b = Math.floor(Math.random() * 256)
  var rgb = 'rgb(' + r + ',' + g + ',' + b + ')'
  return rgb
}
function randomString(n) {
  let str = 'abcdefghijklmnopqrstuvwxyz9876543210'
  let tmp = '',
    i = 0,
    l = str.length
  for (i = 0; i < n; i++) {
    tmp += str.charAt(Math.floor(Math.random() * l))
  }
  return tmp
}

function isJiubaCustom1(wallFlag) {
  return window !== window.top && ['uxmggbrV', 'L1I5oVIi', 'oNf5ly80', '4E160CKn'].includes(wallFlag)
}

let danmuBox = null
const lineObj = {}
let showAreaWidth = 0
let lineShowIng = {}

class Line {
  constructor(id, lineZoom) {
    this.id = id
    const lineBox = document.createElement('div')
    this.box = lineBox
    lineBox.setAttribute('class', `line line-${id}`)
    lineBox.style.height = `calc(${lineZoom} * 10vh)`
    // lineBox.style.backgroundColor = rgb()
    danmuBox.appendChild(lineBox)
  }
  static async appendMsg(msgData, fn) {
    const msgItem = new Msg(msgData)
    const msgBox = msgItem.box
    let lineNum = -1

    while (true) {
      let showArr = Object.keys(lineShowIng)
      // 看后台设置
      if (msgData.position.length !== 3 && !isJiubaCustom1(msgData.wallFlag)) {
        let tmpObj = {}
        const length = Object.keys(lineObj).length
        const count = Math.floor(length / 3)
        // 前 0 ~ count
        // 后 length - count ~ length
        msgData.position.forEach(key => {
          switch (key) {
            case 'top':
              for (let i = 0; i < count; i++) {
                tmpObj[i] = 1
              }
              break
            case 'middle':
              for (let i = count; i < length - 2 * count + count; i++) {
                tmpObj[i] = 1
              }
              break
            case 'bottom':
              for (let i = 0; i < count; i++) {
                tmpObj[length - i - 1] = 1
              }
              break
            default:
              break
          }
        })
        showArr = showArr.filter(item => tmpObj[item])
      }
      if (showArr.length) {
        lineNum = showArr[Math.floor(Math.random() * showArr.length)]
      }
      if (lineNum !== -1) {
        break
      } else {
        await timer(1000)
      }
    }

    const lineBox = lineObj[lineNum].box
    let lineData = lineObj[lineNum].data
    let duration = Math.ceil(Math.random() * 1000 + 4000)
    // 已经正在播放消息的行
    if (!lineData) {
      lineData = lineObj[lineNum].data = {}
    }
    const preLineData = Object.assign({}, lineData)
    msgBox.style.transform = `translate3d(${showAreaWidth}px, 0, 0)`
    lineBox.appendChild(msgBox)

    fn && fn()
    lineData.box = msgBox
    delete lineShowIng[lineNum]
    await timer()
    // 计算出实际尺寸
    const rect = msgBox.getBoundingClientRect()
    const msgWidth = Math.ceil(rect.width) + 20

    if (preLineData.box) {
      const rect = preLineData.box.getBoundingClientRect()
      duration = Math.ceil((rect.left + preLineData.width + msgWidth) / preLineData.speed)
    }
    // speed [ 0.3  0.1 ]
    // [0.01 ~ 1]
    // 后台设置转换成前台使用的速度值
    // 速度在范围外需要重新定义
    const _tmpSpeed = (showAreaWidth + msgWidth) / duration
    if (_tmpSpeed > msgData.speed) {
      // 重新计算的速度不能超过上一个的速度
      let minSpeed = Math.round(msgData.speed * 0.6 * 100) / 100,
        maxSpeed = Math.min(msgData.speed, _tmpSpeed)
      // console.log('重新计算', minSpeed, maxSpeed)
      const speed = Math.random() * (maxSpeed - minSpeed) + minSpeed
      duration = Math.ceil((showAreaWidth + msgWidth) / speed)
    }
    let speed = (showAreaWidth + msgWidth) / duration
    //console.log('路程', showAreaWidth + msgWidth,'耗时', duration, '速度', speed)
    // todo
    msgBox.style.transitionDuration = `${duration}ms`
    await timer()
    // todo
    msgBox.style.transform = `translate3d(-${msgWidth}px, 0, 0)`
    // msgBox.style.transform = `translate3d(-${0}px, 0, 0)`
    lineData = Object.assign(lineData, { startTime: +new Date(), duration, width: msgWidth, speed })
    msgItem.data = lineData
    setTimeout(() => {
      lineShowIng[lineNum] = true
    }, msgWidth / speed)

    // 播放完成后消除索引，销毁对象
    msgBox.addEventListener('transitionend', () => {
      if (lineObj[lineNum].data.box === msgBox) {
        lineObj[lineNum].data = null
      }
      lineBox.removeChild(msgBox)
    })
  }
}
class Msg {
  constructor(msg) {
    this.msg = msg
    const msgBox = document.createElement('div')
    this.box = msgBox
    msgBox.setAttribute('class', `msg ${msg.style}`)
    msgBox.style.opacity = msg.opacity

    const { lineZoom = 1 } = msg
    let emojiStyle = `width: calc(${lineZoom} * 6vh);height: calc(${lineZoom} * 6vh);`
    if (isJiubaCustom1(msg.wallFlag)) {
      msg.style = 'simple-custom'
    }
    //调试
    //msg.style = 'shenian'
    switch (msg.style) {
      // 五彩缤纷
      case 'wcbfdm': {
        const isShowAvatar = msg.showHeadSwitch === 'Y'
        msg.color = msg.textColor === 'DEFAULT' ? '#ffffff' : msg.textColorRgb || '#ffffff'
        msg.backgroundColor =
          msg.bgColor === 'DEFAULT'
            ? randomColor({ luminosity: 'dark' })
            : msg.bgColorRgb || randomColor({ luminosity: 'dark' })
        const mask = require('./theme/wcbfdm/mask.png')
        const bgBoxStyle = `
          position: absolute;
          top: calc(${lineZoom} * .5vh);
          left: 0;
          width: calc(100% - 10vh);
          height: calc(${lineZoom} * 9vh);
          background-color: ${msg.backgroundColor};
          border-radius: calc(${lineZoom} * 10vh) 0 0 calc(${lineZoom} * 10vh);
          z-index: 2;
        `
        const lastStyle = `
          position: absolute;
          top: 0;
          right: -10vh;
          width: 10vh;
          height: 100%;
          background-color: ${msg.backgroundColor};
          -webkit-mask-image: url(${mask});
          -webkit-mask-repeat: no-repeat;
          -webkit-mask-size: 100% 100%;
        `
        const textBoxStyle = `
          line-height: calc(${lineZoom} * 10vh);
          padding: 0 calc(${lineZoom} * 10vh) 0 calc(${lineZoom} * 3vh);
          font-size: calc(${lineZoom} * 4.5vh);
          z-index: 10;
          color:${msg.color};
        `
        const avatarStyle = `
          height:${lineZoom * 7}vh;
          margin-right:${lineZoom * 3.7}vh;
          border-radius:50%;
          margin-left: -${lineZoom * 2.59}vh;
        `
        const avatarHtml = isShowAvatar ? `<img src="${msg.wxUserImage}" style="${avatarStyle}"/>` : ''
        msgBox.innerHTML = `
          <div class="bg-box" style="${bgBoxStyle}"><div style="${lastStyle}"></div></div>
          <div class="text-box" style="${textBoxStyle}">
          ${avatarHtml}
          ${Hi.Emoji.emojiEncode(msg.content, { style: emojiStyle })}
          </div>
        `
        break
      }
      // 恭贺新春
      case 'ghxcdm': {
        const isShowAvatar = msg.showHeadSwitch === 'Y'
        msg.color = msg.textColor === 'DEFAULT' ? '#F7C090' : msg.textColorRgb || '#F7C090'
        msg.backgroundColor = msg.bgColor === 'DEFAULT' ? '#DB2C27' : msg.bgColorRgb || '#DB2C27'

        emojiStyle = `width: calc(${lineZoom} * 4vh);height: calc(${lineZoom} * 4vh);transform: translateY(calc(${lineZoom} * -.2vh));`
        const imgBoxImgStyle = `
          position: absolute;
          top:calc(${lineZoom} * 2.24vh);
          left:calc(${lineZoom} * 3.3vh);
          z-index: 1;
          width: calc(${lineZoom} * 5.7vh);
          height: calc(${lineZoom} * 5.7vh);
          border-radius:50%;
        `
        const imgBoxImgShadeStyle = `
          position: absolute;
          top: calc(${lineZoom}* 1.9vh);
          left: calc(${lineZoom} * 2vh);
          z-index: 2;
          width: calc(${lineZoom} * 8.9vh);
          height: calc(${lineZoom} * 6.2vh);
        `
        const textBoxStyle = `
          line-height: calc(${lineZoom} * 6vh);
          padding: ${isShowAvatar ? `0 calc(${lineZoom} * 3vh) 0 calc(${lineZoom} * 7vh)` : 0};
          font-size: calc(${lineZoom} * 3vh);
          transform: translateY(calc(${lineZoom} * -0.2vh));
          color: ${msg.color};
          font-weight: bold;
          z-index: 10;
          position: relative;
        `
        msgBox.innerHTML = `
        <div style="display: flex;height: calc(${lineZoom} * 6vh)">
          <svg
            version="1.1"
            xmlns="http://www.w3.org/2000/svg"
            x="0px"
            y="0px"
            viewBox="0 0 38.2 76.1"
            style="height: 100%;position:relative;z-index: 0;">
            <path
              d="M38.2,75.2c-15.9,0-18.6-9.2-18.2-15c-8.3-3.3-9.1-8.4-9.9-12.8c-0.8-4.4-4.9-8.4-6.8-9.2
                    c4.2-1.8,7.2-10.3,7.6-12.1c2.3-7.7,6.1-9.2,9.1-10.3C20.7,0.5,38.2,1.1,38.5,1"
              style="fill:${msg.backgroundColor};stroke:${msg.color};stroke-width:2;stroke-miterlimit:10;"/>
          </svg>
          <div style="position: relative;display: flex;align-items: center;z-index:1;margin-right:${lineZoom * -0.5
          }px;">
            <svg
              version="1.1"
              xmlns="http://www.w3.org/2000/svg"
              x="0px"
              y="0px"
              viewBox="0 0 38.2 76.1"
              preserveAspectRatio="none"
              style="z-index: 1;width: 100%;height: 100%;position: absolute;left: 0;top: 0">
              <rect class="rect" x="0" y="2" width="500" height="72.1" style="stroke: none;fill: ${msg.backgroundColor
          }"></rect>
              <line class="line1" x1="0" x2="500" y1="1" y2="1" style="stroke: ${msg.color};stroke-width: 2"></line>
              <line class="line2" x1="0" x2="500" y1="75.1" y2="75.1" style="stroke:${msg.color
          };stroke-width: 2"></line>
            </svg>
            <div class="text-box" style="${textBoxStyle}">${Hi.Emoji.emojiEncode(msg.content, {
            style: emojiStyle,
          })}</div>
          </div>
          <svg
            version="1.1"
            xmlns="http://www.w3.org/2000/svg"
            x="0px"
            y="0px"
            viewBox="0 0 38.2 76.1"
            style="height: 100%;position:relative;z-index: 0;transform: rotate(180deg);margin-left: -1px">
            <path
              d="M38.2,75.1c-15.9,0-18.6-9.2-18.2-15c-8.3-3.3-9.1-8.4-9.9-12.8c-0.8-4.4-4.9-8.4-6.8-9.2
                    c4.2-1.8,7.2-10.3,7.6-12.1c2.3-7.7,6.1-9.2,9.1-10.3C20.7,0.5,38.2,1.1,38.8,0.8"
              style="fill:${msg.backgroundColor};stroke: ${msg.color};stroke-width:2;stroke-miterlimit:10;"/>
          </svg>
          `
        if (isShowAvatar) {
          msgBox.innerHTML += ` <img src="${msg.wxUserImage}" style="${imgBoxImgStyle}"/><img src="${msgAvatarShade}" style="${imgBoxImgShadeStyle}">`
        }
        break
      }
      // 婉约
      case 'simple': {
        msg.color =
          msg.textColor === 'DEFAULT'
            ? randomColor({ luminosity: 'dark' })
            : msg.textColorRgb || randomColor({ luminosity: 'dark' })
        msg.backgroundColor = msg.bgColor === 'DEFAULT' ? '#ffffff' : msg.bgColorRgb || '#ffffff'
        const imgBoxStyle = `
          width: calc(${lineZoom} * 10vh);
          height: calc(${lineZoom} * 10vh);
          border-color: ${msg.color};
          background-color: ${msg.color};
          overflow: hidden;
          border: 0;
          border-radius: 50%;
          z-index: 2;
          display: flex;
        `
        const imgBoxImgStyle = `
          width: calc(100% - 6px);
          height: calc(100% - 6px);
          border: 3px solid transparent;
          border-radius: 50%;
          object-fit: cover;
        `
        const textBoxStyle = `
          line-height: calc(${lineZoom} * 6vh);
          transform: translateX(calc(${lineZoom} * -3vh));
          padding: 0 calc(${lineZoom} * 3vh) 0 calc(${lineZoom} * 4vh);
          border-radius: calc(${lineZoom} * 6vh);
          color: ${msg.color};
          border-color: ${msg.color};
          font-size: calc(${lineZoom} * 4.5vh);
          border: 3px solid;
          background-color: ${msg.backgroundColor};
        `
        const avatarHtml =
          msg.showHeadSwitch === 'Y'
            ? ` <div class="img-box" style="${imgBoxStyle}"><img src="${msg.wxUserImage}" style="${imgBoxImgStyle}"/></div>`
            : ''
        msgBox.innerHTML = `
          ${avatarHtml} 
          <div class="text-box" style="${textBoxStyle}">${Hi.Emoji.emojiEncode(msg.content, {
          style: emojiStyle,
        })}</div>
        `
        break
      }
      case 'simple-custom': {
        msg.color = '#ff9600'
        const imgBoxStyle = `
          width: calc(${lineZoom} * 10vh);
          height: calc(${lineZoom} * 10vh);
          border-color: ${msg.color};
          background-color: ${msg.color};
          overflow: hidden;
          border: 0;
          border-radius: 50%;
          z-index: 2;
          display: flex;
        `
        const imgBoxImgStyle = `
          width: calc(100% - 6px);
          height: calc(100% - 6px);
          border: 3px solid transparent;
          border-radius: 50%;
          object-fit: cover;
        `
        const textBoxStyle = `
          line-height: calc(${lineZoom} * 6vh);
          transform: translateX(calc(${lineZoom} * -3vh));
          padding: 0 calc(${lineZoom} * 3vh) 0 calc(${lineZoom} * 4vh);
          border-radius: 0 calc(${lineZoom} * 6vh) calc(${lineZoom} * 6vh) 0;
          color: ${msg.color};
          font-size: calc(${lineZoom} * 4.5vh);
          background-color: transparent;
        `
        // font-family:kangkang;
        msgBox.innerHTML = `
          <div class="img-box" style="${imgBoxStyle}"><img src="${msg.wxUserImage}" style="${imgBoxImgStyle}"/></div>
          <div class="text-box" style="${textBoxStyle}">${Hi.Emoji.emojiEncode(msg.content, {
          style: emojiStyle,
        })}</div>
        `
        break
      }
      case 'rough': {
        msg.color =
          msg.textColor === 'DEFAULT'
            ? randomColor({ luminosity: 'dark' })
            : msg.textColorRgb || randomColor({ luminosity: 'dark' })
        msg.backgroundColor = msg.bgColor === 'DEFAULT' ? '#ffffff' : msg.bgColorRgb || '#ffffff'
        const isShowAvatar = msg.showHeadSwitch === 'Y'
        const imgBoxImgStyle = `
          width: calc(${lineZoom} * 8vh);
          height: calc(${lineZoom} * 8vh);
          border: 3px solid ${msg.color};
          border-radius: 50%;
          object-fit: cover;
          z-index: 2;position: relative;
        `
        const avatarHtml = isShowAvatar ? ` <img src="${msg.wxUserImage}" style="${imgBoxImgStyle}"/>` : ''
        const textBoxStyle = `
          font-size: calc(${lineZoom} * 4.5vh);
          font-weight: bold;
          color:${msg.color};
          position:relative;
          padding-left: ${lineZoom * (isShowAvatar ? 5.55 : 2.77)}vh;
          padding-right:${isShowAvatar ? `calc(${lineZoom} * 3vh)` : lineZoom * 2.77 + 'vh'};
          line-height: calc(${lineZoom} * 7vh);
          margin-left:${isShowAvatar ? lineZoom * -2.77 : 0}vh;
        `
        msgBox.innerHTML = `
          ${avatarHtml}
          <div class="text-box" style="${textBoxStyle}">
          <div style="position:absolute;left:0;top:0;width:100%;height:100%;background:${msg.backgroundColor
          };opacity:0.2;border-radius: ${lineZoom * 3.7}vh;"></div>
          ${Hi.Emoji.emojiEncode(msg.content, { style: emojiStyle })}
        </div>
        `
        break
      }
      // 虎年样式
      case 'hnysdm': {
        const isShowAvatar = msg.showHeadSwitch === 'Y'
        msg.color = msg.textColor === 'DEFAULT' ? '#ffffff' : msg.textColorRgb || '#ffffff'
        msg.backgroundColor =
          msg.bgColor === 'DEFAULT'
            ? randomColor({ luminosity: 'dark' })
            : msg.bgColorRgb || randomColor({ luminosity: 'dark' })
        const head = isShowAvatar ? require('./theme/hnysdm/head1.png') : require('./theme/hnysdm/head2.png')
        const head_b = isShowAvatar ? require('./theme/hnysdm/head_b1.png') : require('./theme/hnysdm/head_b2.png')

        const avatarStyle = `
          width: ${lineZoom * 6.8}vh;
          height: ${lineZoom * 6.8}vh;
          position: absolute;
          border-radius: 50%;
          left: ${lineZoom * 3.4}vh;
          top:${lineZoom * 2.5}vh;
          margin-left:${lineZoom > 1 ? lineZoom * -0.5 : lineZoom * 0.2}vh;
          z-index: -1;
        `
        const avatarBoxStyle = `
          width: ${lineZoom * 13}vh;
          height: 100%;
          position: absolute;
          background-image: url(${head});
          mask-image:url(${head_b}) ;
          -webkit-mask-image:url('${head_b}') ;
          background-repeat:no-repeat;
          background-size:100% 100%;
          mask-repeat:no-repeat;
          -webkit-mask-repeat:no-repeat;
          mask-size:100% 100%;
          -webkit-mask-size:100% 100%;
          background-color: ${msg.backgroundColor};
        `
        const lastStyle = `
          width: ${lineZoom * 4.3}vh;
          height: 100%;
          position: absolute;
          right:${3 / lineZoom}px;
          top:0;
          background-image: url(${require('./theme/hnysdm/last.png')});
          background-repeat:no-repeat;
          mask-image:url(${require('./theme/hnysdm/last_b.png')}) ;
          -webkit-mask-image:url(${require('./theme/hnysdm/last_b.png')});
          background-repeat:no-repeat;
          background-size:100% 100%;
          mask-repeat:no-repeat;
          -webkit-mask-repeat:no-repeat;
          mask-size:100% 100%;
          -webkit-mask-size:100% 100%;
          background-color: ${msg.backgroundColor};
        `
        const textStyle = `
          height: 100%;
          width:100%;
          mask-image:url(${require('./theme/hnysdm/middle_b.png')}) ;
          -webkit-mask-image:url(${require('./theme/hnysdm/middle_b.png')}) ;
          background-color: ${msg.backgroundColor};
          min-width: 100px;
          background-size:100% 100%;
          mask-size:100% 100%;
          -webkit-mask-size:100% 100%;
          box-sizing: border-box;
          padding: ${lineZoom * 2.5}vh ${lineZoom * 10}px 0 ${lineZoom * 10}px;
          line-height: ${lineZoom * 6.5}vh;
          color: ${msg.color};
          font-size: calc(${lineZoom} * 4.5vh);
          white-space:nowrap;
          box-sizing: border-box;
        `
        const textBoxStyle = ` 
          width: 100%;
          height: 100%;
          padding: 0 ${lineZoom * 3}vh 0 ${lineZoom * 12}vh;
          box-sizing: border-box;
          `
        const avatarHtml = isShowAvatar ? `<img src="${msg.wxUserImage}" style="${avatarStyle}">` : ''
        msgBox.innerHTML = `
          <div style="${avatarBoxStyle}"></div>
          ${avatarHtml}
          <div style="${textBoxStyle}">
            <div style="${textStyle}">${Hi.Emoji.emojiEncode(msg.content, { style: emojiStyle })}</div>
          </div>
          <div style="${lastStyle}"></div>
         `
        break
      }
      // 兔年样式
      case 'tnysdm': {
        const isShowAvatar = msg.showHeadSwitch === 'Y'
        msg.color = msg.textColor === 'DEFAULT' ? '#ffffff' : msg.textColorRgb || '#ffffff'
        msg.backgroundColor =
          msg.bgColor === 'DEFAULT'
            ? randomColor({ luminosity: 'dark' })
            : msg.bgColorRgb || randomColor({ luminosity: 'dark' })
        const head = isShowAvatar ? require('./theme/tnysdm/head1.png') : require('./theme/tnysdm/head2.png')

        let avatarStyleLeft = lineZoom * 10.6 * 0.58
        if (lineZoom > 1) {
          avatarStyleLeft = avatarStyleLeft - Math.pow(lineZoom - 1, 2)
        } else if (lineZoom < 0.8) {
          avatarStyleLeft = avatarStyleLeft + 1 - lineZoom
        }
        const avatarStyle = `
          width: ${lineZoom * 5.8}vh;
          height: ${lineZoom * 5.8}vh;
          position: absolute;
          border-radius: 50%;
          left: ${avatarStyleLeft}vh;
          top:${lineZoom * 3.6}vh;
          z-index: -1;
        `
        const avatarBoxStyle = `
          width: ${lineZoom * 10.6}vh;
          height: 100%;
          position: absolute;
          mask-image:url(${head}) ;
          -webkit-mask-image:url('${head}') ;
          background-repeat:no-repeat;
          background-size:100% 100%;
          mask-repeat:no-repeat;
          -webkit-mask-repeat:no-repeat;
          mask-size:100% 100%;
          -webkit-mask-size:100% 100%;
          background-color: ${msg.backgroundColor};
        `
        const lastStyle = `
          width: ${lineZoom * 4.3}vh;
          height: 100%;
          position: absolute;
          right:${3 / lineZoom}px;
          top:0;
          mask-image:url(${require('./theme/tnysdm/last.png')}) ;
          -webkit-mask-image:url(${require('./theme/tnysdm/last.png')});
          background-repeat:no-repeat;
          background-size:100% 100%;
          mask-repeat:no-repeat;
          -webkit-mask-repeat:no-repeat;
          mask-size:100% 100%;
          -webkit-mask-size:100% 100%;
          background-color: ${msg.backgroundColor};
        `
        const textStyle = `
          height: 100%;
          width:100%;
          mask-image:url(${require('./theme/tnysdm/middle.png')}) ;
          -webkit-mask-image:url(${require('./theme/tnysdm/middle.png')}) ;
          background-color: ${msg.backgroundColor};
          min-width: 100px;
          background-size:100% 100%;
          mask-size:100% 100%;
          -webkit-mask-size:100% 100%;
          box-sizing: border-box;
          padding: ${lineZoom * 2.5}vh ${lineZoom * 10}px 0 ${lineZoom * 10.6}px;
          line-height: ${lineZoom * 6.5}vh;
          color: ${msg.color};
          font-size: calc(${lineZoom} * 4.5vh);
          white-space:nowrap;
          box-sizing: border-box;
        `
        const textBoxStyle = `
          width: 100%;
          height: 100%;
          padding: 0 ${lineZoom * 3}vh 0 ${lineZoom * 10.6 - 0.2}vh;
          box-sizing: border-box;
          `
        const avatarHtml = isShowAvatar ? `<img src="${msg.wxUserImage}" style="${avatarStyle}">` : ''
        msgBox.innerHTML = `
          <div style="${avatarBoxStyle}"></div>
          ${avatarHtml}
          <div style="${textBoxStyle}">
            <div style="${textStyle}">${Hi.Emoji.emojiEncode(msg.content, { style: emojiStyle })}</div>
          </div>
          <div style="${lastStyle}"></div>
         `
        break
      }
      // 龙年样式
      case 'lnysdm': {
        const isShowAvatar = msg.showHeadSwitch === 'Y'
        msg.color = msg.textColor === 'DEFAULT' ? '#ffffff' : msg.textColorRgb || '#ffffff'
        msg.backgroundColor =
          msg.bgColor === 'DEFAULT'
            ? randomColor({ luminosity: 'dark' })
            : msg.bgColorRgb || randomColor({ luminosity: 'dark' })
        const head = isShowAvatar ? require('./theme/lnysdm/head1.png') : require('./theme/lnysdm/head2.png')
        const head_b = isShowAvatar ? require('./theme/lnysdm/head_b1.png') : require('./theme/lnysdm/head_b2.png')

        const avatarStyle = `
          width: ${lineZoom * 4.6}vh;
          height: ${lineZoom * 4.6}vh;
          position: absolute;
          border-radius: 50%;
          left: ${lineZoom * 15.2}vh;
          top:${lineZoom * 4}vh;
          margin-left:${lineZoom > 1 ? lineZoom * -0.5 : lineZoom * 0.2}vh;
          z-index: -1;
        `
        const avatarBoxStyle = `
          width: ${lineZoom * 19.5}vh;
          height: 100%;
          position: absolute;
          background-image: url(${head});
          mask-image:url(${head_b}) ;
          -webkit-mask-image:url('${head_b}') ;
          background-repeat:no-repeat;
          background-size:100% 100%;
          mask-repeat:no-repeat;
          -webkit-mask-repeat:no-repeat;
          mask-size:100% 100%;
          -webkit-mask-size:100% 100%;
          background-color: ${msg.backgroundColor};
        `
        const lastStyle = `
          width: ${lineZoom * 11.5}vh;
          height: 100%;
          position: absolute;
          right:${3 / lineZoom}px;
          top:0;
          background-image: url(${require('./theme/lnysdm/last.png')});
          background-repeat:no-repeat;
          mask-image:url(${require('./theme/lnysdm/last_b.png')}) ;
          -webkit-mask-image:url(${require('./theme/lnysdm/last_b.png')});
          background-repeat:no-repeat;
          background-size:100% 100%;
          mask-repeat:no-repeat;
          -webkit-mask-repeat:no-repeat;
          mask-size:100% 100%;
          -webkit-mask-size:100% 100%;
          background-color: ${msg.backgroundColor};
        `
        const textStyle = `
          height: 100%;
          width:100%;
          mask-image:url(${require('./theme/lnysdm/middle_b.png')}) ;
          -webkit-mask-image:url(${require('./theme/lnysdm/middle_b.png')}) ;
          background-color: ${msg.backgroundColor};
          min-width: 100px;
          background-size:100% 100%;
          mask-size:100% 100%;
          -webkit-mask-size:100% 100%;
          box-sizing: border-box;
          padding: ${lineZoom * 2.6}vh ${lineZoom * 12}px 0 ${lineZoom * 2}px;
          line-height: ${lineZoom * 6.5}vh;
          color: ${msg.color};
          font-size: calc(${lineZoom} * 3.5vh);
          white-space:nowrap;
          box-sizing: border-box;
        `
        const textBoxStyle = ` 
          width: 100%;
          height: 100%;
          padding: 0 ${lineZoom * 10}vh 0 ${isShowAvatar ? lineZoom * 19 : lineZoom *15}vh;
          box-sizing: border-box;
          `
        const avatarHtml = isShowAvatar ? `<img src="${msg.wxUserImage}" style="${avatarStyle}">` : ''
        msgBox.innerHTML = `
          <div style="${avatarBoxStyle}"></div>
          ${avatarHtml}
          <div style="${textBoxStyle}">
            <div style="${textStyle}">${Hi.Emoji.emojiEncode(msg.content, { style: emojiStyle })}</div>
          </div>
          <div style="${lastStyle}"></div>
         `
        break
      }
       // 蛇年样式
      case 'shenian': {
        const isShowAvatar = msg.showHeadSwitch === 'Y'
        msg.color = msg.textColor === 'DEFAULT' ? '#ffffff' : msg.textColorRgb || '#ffffff'
        msg.backgroundColor =
          msg.bgColor === 'DEFAULT'
            ? randomColor({ luminosity: 'dark' })
            : msg.bgColorRgb || randomColor({ luminosity: 'dark' })
        const head = isShowAvatar ? require('./theme/shenian/head1.png') : require('./theme/shenian/head2.png')
        const head_b = isShowAvatar ? require('./theme/shenian/head_b1.png') : require('./theme/shenian/head_b2.png')

        const avatarStyle = `
          width: ${lineZoom * 7}vh;
          height: ${lineZoom * 7}vh;
          position: absolute;
          border-radius: 50%;
          left: ${lineZoom * 5.3}vh;
          top:${lineZoom * 2.3}vh;
          margin-left:${lineZoom > 1 ? lineZoom * -0.5 : lineZoom * 0.2}vh;
          z-index: -1;
        `
        const avatarBoxStyle = `
          width: ${lineZoom * 11.85}vh;
          height: 100%;
          position: absolute;
          background-image: url(${head});
          mask-image:url(${head_b}) ;
          -webkit-mask-image:url('${head_b}') ;
          background-repeat:no-repeat;
          background-size:100% 100%;
          mask-repeat:no-repeat;
          -webkit-mask-repeat:no-repeat;
          mask-size:100% 100%;
          -webkit-mask-size:100% 100%;
          background-color: ${msg.backgroundColor};
        `
        const lastStyle = `
          width: ${lineZoom * 7.04}vh;
          height: 100%;
          position: absolute;
          right:${3 / lineZoom}px;
          top:0;
          background-image: url(${require('./theme/shenian/last.png')});
          background-repeat:no-repeat;
          mask-image:url(${require('./theme/shenian/last_b.png')}) ;
          -webkit-mask-image:url(${require('./theme/shenian/last_b.png')});
          background-repeat:no-repeat;
          background-size:100% 100%;
          mask-repeat:no-repeat;
          -webkit-mask-repeat:no-repeat;
          mask-size:100% 100%;
          -webkit-mask-size:100% 100%;
          background-color: ${msg.backgroundColor};
        `
        const textStyle = `
          height: 100%;
          width:100%;
          mask-image:url(${require('./theme/shenian/middle_b.png')}) ;
          -webkit-mask-image:url(${require('./theme/shenian/middle_b.png')}) ;
          background-color: ${msg.backgroundColor};
          min-width: 100px;
          background-size:100% 100%;
          mask-size:100% 100%;
          -webkit-mask-size:100% 100%;
          box-sizing: border-box;
          padding: ${lineZoom * 2.3}vh ${lineZoom * 8}px 0 ${lineZoom * 2}px;
          line-height: ${lineZoom * 6.5}vh;
          color: ${msg.color};
          font-size: calc(${lineZoom} * 3.5vh);
          white-space:nowrap;
          box-sizing: border-box;
        `
        const textBoxStyle = ` 
          width: 100%;
          height: 100%;
          padding: 0 ${lineZoom * 6}vh 0 ${isShowAvatar ? lineZoom * 11 : lineZoom *11.5}vh;
          box-sizing: border-box;
          `
        const avatarHtml = isShowAvatar ? `<img src="${msg.wxUserImage}" style="${avatarStyle}">` : ''
        msgBox.innerHTML = `
          <div style="${avatarBoxStyle}"></div>
          ${avatarHtml}
          <div style="${textBoxStyle}">
            <div style="${textStyle}">${Hi.Emoji.emojiEncode(msg.content, { style: emojiStyle })}</div>
          </div>
          <div style="${lastStyle}"></div>
         `
        break
      }
      default: {
        emojiStyle = `width: calc(${lineZoom} * 6vh);height: calc(${lineZoom} * 6vh);`
        msgBox.innerHTML = `
          <div class="text-box" style="font-size: calc(${lineZoom} * 4.5vh); color: ${msg.color
          };">${Hi.Emoji.emojiEncode(msg.content, {
            style: emojiStyle,
          })}</div>
        `
        break
      }
    }
  }
}

import { mapActions } from 'vuex'
export default {
  name: 'danmu',
  mixins: [relationUserMixin],
  data() {
    return {
      isDanmuShow: false,
      danmuIndex: 0,

      pause: false,
    }
  },
  computed: {
    ...mapState('danmu', ['danmuMsgArr']),
    ...mapState('wall', ['wallFlag']),
    ...mapGetters({
      isInitfinish: 'wall/isInitfinish',
      zoom: 'common/zoom',
      danmuConfig: 'danmu/getWalldanmuConfig',
    }),
    position() {
      let position = ''
      if (this.danmuConfig.position) {
        try {
          position = JSON.parse(this.danmuConfig.position || '[]')
        } catch (err) {
          console.error(err)
        }
      }
      return position
    },
    // 计算行数
    lineCount() {
      if (isJiubaCustom1(this.wallFlag)) {
        return 1
      }
      // fontSize 后台是 16 - 50 先转换成 0 - 100 获取到百分比  假如字体是33，正好是10行
      const fontSize = parseFloat(this.danmuConfig.fontsize || '33')
      return Math.floor(15 - ((fontSize - 16) / 34) * 10)
    },
    lineZoom() {
      // 按照行数10行进行缩放
      return Math.floor((10 / this.lineCount) * 100) / 100
    },
    speed() {
      // 速度最小和最大值
      const tmp = this.danmuConfig ? this.danmuConfig.speed : 25
      // 0 ~ 50
      let sp = Math.round((tmp / 50) * 0.7 * 100) / 100
      sp = Math.max(0.1, sp)
      return sp
    },
    density() {
      // 根据后台密度配置决定时间多少合适
      const density = 100 - (this.danmuConfig ? this.danmuConfig.density || 50 : 50)
      return Math.round(density * 10)
    },
    opacity() {
      return (this.danmuConfig.opacity == undefined ? 100 : this.danmuConfig.opacity) / 100
    },
    danmuBoxStyle() {
      return {
        opacity: this.isDanmuShow ? 1 : 0,
        zoom: 1 / this.zoom,
        fontSize: `calc(32px * ${this.lineZoom})`,
      }
    },
  },
  watch: {
    async isDanmuShow(v, o) {
      if (v && !o) {
        await this.recusionTool.call(this, this.go.bind(this))
      }
    },
    isInitfinish: {
      immediate: true,
      handler(v) {
        if (v) {
          this.init()
        }
      },
    },
  },
  methods: {
    ...mapActions({
      pollNormalMsg: 'msg/pollNormalMsg',
      closePollMsg: 'msg/closePollMsg',
    }),
    async recusionTool(f) {
      while (f && f instanceof Function) {
        f = await f.call(this)
      }
    },
    getNextMsg() {
      let msg = null
      msg = this.danmuMsgArr[this.danmuIndex % this.danmuMsgArr.length]
      if (this.danmuIndex === this.danmuMsgArr.length && this.danmuConfig.loop === 'N') {
        return null
      }
      this.danmuIndex++
      if (!msg || msg.contentType !== 'text') {
        return null
      }
      let tempNum = parseInt(Math.random() * 6)
      let resultMsg = Hi.Object.copy(msg)
      resultMsg.style = this.danmuConfig.style
      resultMsg.speed = this.speed
      // console.log('基准速度', this.speed)
      resultMsg.opacity = this.opacity
      resultMsg.position = this.position
      resultMsg.lineZoom = this.lineZoom

      resultMsg.showHeadSwitch = this.danmuConfig.showHeadSwitch
      resultMsg.textColor = this.danmuConfig.textColor
      resultMsg.bgColor = this.danmuConfig.bgColor
      resultMsg.textColorRgb = this.danmuConfig.textColorRgb
      resultMsg.bgColorRgb = this.danmuConfig.bgColorRgb

      resultMsg.wxUserImage = this.getWxuserImage(resultMsg.wxUserId)
      resultMsg.wxUserNickname = this.getNickName(resultMsg.wxUserId)
      resultMsg.wallFlag = this.wallFlag
      return resultMsg
    },
    async go() {
      if (this.isDanmuShow) {
        let msg = this.getNextMsg()
        if (msg && msg.content && !msg.del && !this.pause) {
          // 假如开启了显示昵称  这里吧 文本内容组装上 名称
          if (this.danmuConfig.shownicknameSwtich === 'Y') {
            msg.content = `${msg.wxUserNickname}: ${msg.content}`
          }
          await new Promise(resolve => {
            Line.appendMsg(msg, resolve).catch(err => {
              console.error(err)
              resolve()
            })
          })
        }
        await timer(this.density)
        // todo调试，可以注释掉下面的循环
        return this.go.bind(this)
      }
      return 'over'
    },
    async init() {
      await this.$nextTick()
      danmuBox = this.$el
      let handler = 0
      const resizeShowArea = () => {
        clearTimeout(handler)
        handler = setTimeout(() => {
          showAreaWidth = Math.ceil(danmuBox.getBoundingClientRect().width)
        }, 500)
      }
      window.addEventListener('resize', resizeShowArea)
      showAreaWidth = Math.ceil(danmuBox.getBoundingClientRect().width)

      this.$watch(
        'lineCount',
        async (v, o) => {
          if (v) {
            this.pause = true
            await timer(1000)
            try {
              if (v !== o && danmuBox) {
                // 根据 lineCount 重新整理，多退少补
                const lineObjArr = Object.keys(lineObj)
                if (lineObjArr.length > v) {
                  // 多了删除
                  let maxNum = Math.max(...lineObjArr, 0)
                  for (let i = 0; i < lineObjArr.length - v; i++) {
                    danmuBox.removeChild(lineObj[maxNum - i].box)
                    delete lineObj[maxNum - i]
                  }
                } else {
                  let maxNum = Math.max(...lineObjArr, 0)
                  maxNum = maxNum ? maxNum + 1 : maxNum
                  for (let i = 0; i < v - lineObjArr.length; i++) {
                    const lineNum = maxNum + i
                    const line = new Line(lineNum, this.lineZoom)
                    lineObj[lineNum] = line
                    lineShowIng[lineNum] = true
                  }
                }
                Object.keys(lineObj).forEach(key => {
                  lineObj[key].box.style.height = `calc(${this.lineZoom} * 10vh)`
                  lineObj[key].box.innerHTML = ''
                  lineShowIng[key] = true
                })
              }
            } catch (err) {
              console.error(err)
            }
            this.pause = false
          }
        },
        {
          immediate: true,
        },
      )

      this.$bus.$on('isDnmuOpen', isDnmuOpen => {
        if (isDnmuOpen) {
          this.pollNormalMsg()
        } else {
          this.closePollMsg()
        }
        // 开关控制
        this.isDanmuShow = isDnmuOpen
      })
    },
  },
}
</script>
<style scoped lang="stylus">
@font-face
  font-family "kangkang"
  src url("./kangkang.ttf") format("truetype")
.danmu-box
  width 100%
  height 100%
  overflow hidden
  color #fff
  z-index 99
  transition opacity 0.3s
  >>>.line
    width 100%
    display flex
    align-items center
    position relative
    .msg
      height 100%
      display inline-flex
      align-items center
      transition-timing-function linear
      position absolute
      color #fff
      padding 0 10px
      .text-box
        z-index 1
        white-space nowrap
        position relative
        display flex
        align-items center
        justify-content center
        // margin-top 4px
        img
          height 100%
          object-fit cover
</style>
