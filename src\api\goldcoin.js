import api from './api'
export default {
  // 当前轮活动信息
  read: postData => api.fetchBaseData('/pro/hxc/web/progoldcoin/read.htm', postData),
  again: postData => api.fetchBaseData('/pro/hxc/web/progoldcoin/again.htm', postData),
  go: postData => api.fetchBaseData('/pro/hxc/web/progoldcoin/go.htm', postData),
  ranking: postData => api.fetchBaseData('/pro/hxc/web/progoldcoin/ranking.htm', postData),
  switch: postData => api.fetchBaseData('/pro/hxc/web/progoldcoin/switch.htm', postData),
  awardslist: postData => api.fetchBaseData('/pro/hxc/web/progoldcoinawards/list.htm', postData),
}
