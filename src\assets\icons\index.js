import Vue from 'vue'
import Icon from 'vue-awesome/components/Icon'
import 'vue-awesome/icons/flag'
Vue.component('icon', Icon)

// 注册自定义图标
Icon.register({
  delete_fill: {
    width: 1408,
    height: 1792,
    paths: [
      {
        d:
          'M649.824 604.576a31.968 31.968 0 1 1-45.248 45.248L505.6 550.848l-98.976 98.976a31.904 31.904 0 0 1-45.248 0 32 32 0 0 1 0-45.248l98.976-98.976-98.976-98.976a32 32 0 0 1 45.248-45.248l98.976 98.976 98.976-98.976a32 32 0 0 1 45.248 45.248L550.848 505.6l98.976 98.976zM512 128C300.288 128 128 300.288 128 512c0 211.744 172.288 384 384 384 211.744 0 384-172.256 384-384 0-211.712-172.256-384-384-384z',
      },
    ],
  },
})
Icon.register({
  close: {
    width: 1408,
    height: 1792,
    paths: [
      {
        d:
          'M1298 1322q0 40-28 68l-136 136q-28 28-68 28t-68-28l-294-294-294 294q-28 28-68 28t-68-28l-136-136q-28-28-28-68t28-68l294-294-294-294q-28-28-28-68t28-68l136-136q28-28 68-28t68 28l294 294 294-294q28-28 68-28t68 28l136 136q28 28 28 68t-28 68l-294 294 294 294q28 28 28 68z',
      },
    ],
  },
})
// 大屏幕右下角控制按钮
Icon.register({
  control_play: {
    width: 36,
    height: 36,
    paths: [
      {
        d:
          'M18,36C8.075,36,0,27.925,0,18C0,8.075,8.075,0,18,0c9.925,0,18,8.075,18,18 C36,27.925,27.925,36,18,36z M18,2C9.178,2,2,9.178,2,18s7.178,16,16,16s16-7.178,16-16S26.822,2,18,2z',
      },
      {
        d:
          'M28,18c0-0.352-0.192-0.646-0.467-0.825L13.537,8.178C13.379,8.075,13.202,8,13,8 c-0.552,0-1,0.448-1,1c0,0.032,0,18,0,18h0c0,0.553,0.448,1,1,1c0.195,0,0.537-0.157,0.537-0.157l13.996-9.018 C27.808,18.647,28,18.353,28,18z M26,18.049l0.009-0.005c0,0.003,0.002,0.007,0.002,0.01L26,18.049z',
      },
    ],
  },
})
Icon.register({
  control_pause: {
    width: 36,
    height: 36,
    raw:
      '<path d="M18,36C8.075,36,0,27.925,0,18C0,8.075,8.075,0,18,0c9.925,0,18,8.075,18,18 C36,27.925,27.925,36,18,36z M18,2C9.178,2,2,9.178,2,18s7.178,16,16,16s16-7.178,16-16S26.822,2,18,2z"/><rect x="10" y="10" width="6" height="16"/><rect x="20" y="10" width="6" height="16"/>',
  },
})
Icon.register({
  control_load: {
    width: 36,
    height: 36,
    raw:
      '<linearGradient id="SVGID_1_" gradientUnits="userSpaceOnUse" x1="0" y1="27" x2="36" y2="27" gradientTransform="matrix(4.366649e-008 1 1 -4.366649e-008 -1.313950e-004 -3.568862e-004)"><stop  offset="0" style="stop-color:#FFFFFF"/><stop  offset="1" style="stop-color:#617394"/></linearGradient><path fill="url(#SVGID_1_)" d="M18.001,34C26.822,34,34,26.822,34,17.999C34,9.178,26.822,2,18,2V0c9.924,0.001,18,8.075,18,18C36,27.925,27.925,36,18.001,36V34z"/><path d="M18,2C9.177,2,2,9.178,2,18s7.178,16,16,16v2C8.075,36,0,27.925,0,18S8.075,0,18,0V2z"/>',
  },
})
Icon.register({
  control_first: {
    width: 36,
    height: 36,
    raw:
      '<path enable-background="new" d="M0,18C0,8.075,8.075,0,18,0s18,8.075,18,18s-8.075,18-18,18 S0,27.925,0,18z M2,18c0,8.822,7.178,16,16,16c8.821,0,16-7.178,16-16c0-8.822-7.179-16-16-16C9.178,2,2,9.178,2,18z"/><rect x="7" y="12" enable-background="new" width="4" height="12"/><path enable-background="new" d="M12.199,18.001c0,0.195,0.107,0.356,0.258,0.454l7.696,4.959 c0,0,0.188,0.087,0.296,0.087c0.304,0,0.55-0.245,0.55-0.551H21c0,0,0-1.89,0-4.019l6.955,4.481c0,0,0.188,0.087,0.295,0.087 c0.304,0,0.55-0.245,0.55-0.551h0.001c0,0-0.001-9.881-0.001-9.899c0-0.304-0.246-0.55-0.55-0.55c-0.111,0-0.209,0.041-0.295,0.097 l-6.956,4.471c0-2.124,0-4.011,0-4.019c0-0.304-0.246-0.55-0.55-0.55c-0.111,0-0.209,0.041-0.296,0.098l-7.698,4.949 C12.307,17.644,12.199,17.807,12.199,18.001z M21.095,18.025l0.006,0.003l-0.006,0.002C21.095,18.028,21.095,18.026,21.095,18.025z M13.294,18.024l0.006,0.003l-0.006,0.003C13.294,18.028,13.294,18.026,13.294,18.024z"/>',
  },
})
Icon.register({
  control_last: {
    width: 36,
    height: 36,
    raw:
      '<path d="M18,36C8.075,36,0,27.925,0,18C0,8.075,8.075,0,18,0c9.925,0,18,8.075,18,18 C36,27.925,27.925,36,18,36z M18,2C9.178,2,2,9.178,2,18s7.178,16,16,16s16-7.178,16-16S26.822,2,18,2z"/><rect x="25" y="12" width="4" height="12"/><path d="M23.545,17.546l-7.699-4.949c-0.086-0.057-0.184-0.098-0.295-0.098 c-0.304,0-0.55,0.246-0.55,0.55c0,0.008,0,1.895,0,4.019l-6.956-4.471C7.959,12.541,7.861,12.5,7.75,12.5 c-0.304,0-0.55,0.246-0.55,0.55c0,0.018-0.001,9.899-0.001,9.899H7.2c0,0.306,0.246,0.551,0.55,0.551 c0.107,0,0.295-0.087,0.295-0.087L15,18.932c0,2.128,0,4.018,0,4.018h0.001c0,0.306,0.246,0.551,0.55,0.551 c0.107,0,0.295-0.087,0.295-0.087l7.697-4.959c0.151-0.098,0.258-0.259,0.258-0.454C23.801,17.807,23.694,17.644,23.545,17.546z M14.905,18.03l-0.006-0.002l0.006-0.003C14.905,18.026,14.905,18.028,14.905,18.03z M22.706,18.03L22.7,18.027l0.006-0.003 C22.706,18.026,22.706,18.028,22.706,18.03z"/>',
  },
})
Icon.register({
  control_prev: {
    width: 36,
    height: 36,
    raw:
      '<g id="aa"><path enable-background="new" d="M0,18C0,8.075,8.075,0,18,0s18,8.075,18,18s-8.075,18-18,18 S0,27.925,0,18z M2,18c0,8.822,7.178,16,16,16c8.821,0,16-7.178,16-16c0-8.822-7.179-16-16-16C9.178,2,2,9.178,2,18z"/></g><g id="bb"><path enable-background="new" d="M14.963,18.639l10.846,6.989c0,0,0.266,0.122,0.416,0.122 c0.428,0,0.775-0.346,0.775-0.775l0,0c0,0,0-13.925,0-13.95c0-0.428-0.349-0.775-0.775-0.775c-0.157,0-0.294,0.058-0.416,0.138 l-10.848,6.973C14.75,17.499,14.6,17.727,14.6,18S14.75,18.501,14.963,18.639z M16.143,18.043c0-0.003,0-0.006,0-0.009l0.008,0.005 L16.143,18.043z"/><rect x="9" y="10" enable-background="new" width="4" height="16"/></g>',
  },
})
Icon.register({
  control_next: {
    width: 36,
    height: 36,
    raw:
      '<g id="图层_8"><path d="M18,36C8.075,36,0,27.925,0,18C0,8.075,8.075,0,18,0c9.925,0,18,8.075,18,18 C36,27.925,27.925,36,18,36z M18,2C9.178,2,2,9.178,2,18s7.178,16,16,16s16-7.178,16-16S26.822,2,18,2z"/></g><g id="图层_4"><path d="M21.4,18c0-0.273-0.15-0.501-0.361-0.639l-10.848-6.973c-0.122-0.08-0.259-0.138-0.416-0.138 C9.348,10.25,9,10.597,9,11.025c0,0.025,0,13.95,0,13.95h0c0,0.43,0.347,0.775,0.775,0.775c0.151,0,0.416-0.122,0.416-0.122 l10.846-6.989C21.25,18.501,21.4,18.273,21.4,18z M19.85,18.039l0.008-0.005c0,0.003,0,0.006,0,0.009L19.85,18.039z"/><rect x="23" y="10" width="4" height="16"/></g>',
  },
})
Icon.register({
  control_up: {
    width: 36,
    height: 36,
    raw:
      '<path opacity="0.8" fill="#617394" d="M18,0C8.075,0,0,8.075,0,18c0,9.925,8.075,18,18,18c9.925,0,18-8.075,18-18 C36,8.075,27.925,0,18,0z M18,34C9.178,34,2,26.822,2,18S9.178,2,18,2s16,7.178,16,16S26.822,34,18,34z"/><path opacity="0.8" fill="#617394" d="M18,8l-9.096,9.733c-0.7,0.854-0.574,2.115,0.281,2.814C10.04,21.246,11.3 21.121,12,20.267 l4-4.355V26c0,1.104,0.896,2,2,2s2-0.896,2-2V15.911l4,4.355C24.396,20.75,24.97,21,25.549,21c0.445,0,0.894-0.148 1.266-0.452 c0.854-0.699,0.98-1.96,0.281-2.814L18,8z"/>',
  },
})
Icon.register({
  control_down: {
    width: 36,
    height: 36,
    raw:
      '<path opacity="0.8" fill="#617394" d="M36,18c0-9.925-8.075-18-18-18C8.075,0,0,8.075,0,18c0,9.926,8.075,18,18,18 C27.925,36,36,27.926,36,18z M34,18c0,8.822-7.178,16-16,16S2,26.822,2,18S9.178,2,18,2S34,9.178,34,18z"/><path opacity="0.8" fill="#617394" d="M18,28l-9.096-9.733c-0.7-0.854-0.574-2.115,0.281-2.814C10.04,14.754,11.3 14.879,12,15.733 l4,4.355V10c0-1.104,0.896-2,2-2s2,0.896,2,2v10.089l4-4.355C24.396,15.25,24.97,15,25.549,15c0.445,0,0.894,0.148 1.266,0.452 c0.854,0.699,0.98,1.96,0.281,2.814L18,28z"/>',
  },
})
// 大屏幕主题
Icon.register({
  wall_full: {
    width: 1024,
    height: 1024,
    paths: [{ d: 'M0,0v1024h1024V0H0z M960,960H64V64h896V960z' }],
  },
})
Icon.register({
  wall_left: {
    width: 1024,
    height: 1024,
    paths: [{ d: 'M0,0v1024h1024V0H0z M64,64h256v896H64V64z M960,960H384V64h576V960z' }],
  },
})
Icon.register({
  wall_right: {
    width: 1024,
    height: 1024,
    paths: [{ d: 'M0,0v1024h1024V0H0z M64,64h576v896H64V64z M960,960H704V64h256V960z' }],
  },
})
// 倒计时
Icon.register({
  count_down_s1_1: {
    width: 800,
    height: 800,
    raw:
      '<path fill-rule="evenodd" clip-rule="evenodd" fill="#FEC500" d="M350.533,200.521c-47.734,32.057-100.318,51.462-157.003,60.144 c-1.424,0.219-3.354,1.526-4.094-1.107c-1.692-1.789-1.295-4.054-1.297-6.17c-0.032-40.495-0.037-80.99-0.008-121.486 c0.004-6.643,0.554-7.297,7.301-9.277c44.083-12.946,86.671-29.685,127.654-50.461c31.421-15.929,61.381-34.255,89.74-55.169 c2.432-1.792,4.898-3.43,7.776-4.387c32.65-0.035,65.301-0.032,97.951-0.167c2.985-0.013,3.833,1.094,3.547,3.775 c-0.105,0.988-0.017,1.997-0.017,2.997c0,253.215,0,506.431,0,759.646c0,6.641-0.001,6.644-6.599,6.641 c-50.643-0.017-101.285-0.035-151.928-0.054c-1.523-1.581-1.593-3.597-1.649-5.604c-0.066-2.332-0.033-4.665-0.033-6.997 c-0.001-189.434-0.001-378.866,0-568.299c0-1.167,0.062-2.337-0.016-3.498c-0.117-1.781,0.748-4.202-0.878-5.154 c-2.064-1.207-3.436,1.283-4.997,2.306C354.281,199.313,352.549,200.209,350.533,200.521z"/> <path fill-rule="evenodd" clip-rule="evenodd" fill="#F3E09F" d="M350.533,200.521c3.104-2.495,6.181-5.022,9.329-7.46 c0.748-0.58,1.883-1.37,2.575-1.166c1.408,0.415,0.795,1.957,0.866,3.045c0.087,1.326,0.021,2.663,0.021,3.995 c0,193.178-0.001,386.355,0.01,579.533c0,2.326,0.146,4.651,0.223,6.977c-11.943,0.2-11.943,0.2-11.943-11.422 c0-188.964,0.004-377.928-0.051-566.893C351.563,204.936,352.587,202.447,350.533,200.521z"/> <path fill-rule="evenodd" clip-rule="evenodd" fill="#F3E09F" d="M420.603,12.606c-19.706,15.681-40.823,29.27-62.525,41.963 c-51.099,29.887-105.329,52.501-162.063,69.17c-5.041,1.481-6.515,3.631-6.481,8.881c0.258,40.317,0.145,80.638,0.131,120.957 c0,1.993-0.148,3.986-0.228,5.979c-2.735,1.53-5.914,0.558-8.776,1.572c-1.453,0.516-2.062-0.612-2.082-1.986 c-0.017-1.165-0.024-2.33-0.024-3.496c-0.001-41.45,0.048-82.9-0.078-124.351c-0.013-4.076,0.893-5.891,5.252-7.189 c40.021-11.93,78.994-26.604,116.609-44.872c37.628-18.274,73.47-39.423,106.783-64.766 C411.349,11.254,416.066,12.895,420.603,12.606z"/>',
  },
})
Icon.register({
  count_down_s1_2: {
    width: 800,
    height: 800,
    raw:
      '<path fill-rule="evenodd" clip-rule="evenodd" fill="#FEC500" d="M379.493,142.51c-49.712-2.444-95.327,11.032-138.049,35.628c-19.088,10.989-36.808,23.918-53.514,38.259c-1.346,1.155-2.454,2.749-4.404,3.077c-0.53-4.745-1.498-9.438-1.496-14.245c0.016-39.47,0.127-78.94-0.115-118.409c-0.038-6.085,1.943-9.765,7.132-12.896c38.498-23.228,79.264-40.609,123.503-49.581c15.288-3.101,30.698-5.171,46.12-7.28c3.271-0.447,6.633,0.511,9.888-0.505c22.962-1.436,45.928-1.885,68.889-0.048c53.946,4.314,103.951,19.181,146.005,55.01c33.146,28.241,54.023,63.941,62.33,106.527c11.782,60.403,6.161,118.985-21.509,174.593c-19.579,39.347-47.639,72.011-80.581,100.784c-33.309,29.093-69.621,54.203-105.204,80.3c-25.389,18.621-50.427,37.729-72.779,60.034c-11.478,11.453-21.716,23.915-28.608,38.787c-2.246,4.848-3.86,9.885-4.706,15.203c-0.598,3.754,0.144,5.785,4.437,5.155c1.305-0.191,2.662-0.029,3.995-0.029c98.153,0,196.307-0.001,294.461,0.002c4.164,0,8.352,0.298,12.488-0.038c4.001-0.325,4.982,1.088,4.967,4.999c-0.148,36.327-0.087,72.656-0.088,108.984c0,4.666-0.135,9.338,0.052,13.996c0.108,2.729-0.619,4.106-3.567,3.702c-0.981-0.135-1.996-0.021-2.996-0.021c-163.479,0-326.957,0.001-490.435-0.008c-1.995,0-3.991-0.124-5.986-0.189c-1.923-1.452-1.495-3.593-1.5-5.526c-0.045-19.328-0.533-38.673,0.08-57.982c1.412-44.494,12.356-86.375,34.133-125.537c19.999-35.965,45.804-67.08,75.919-94.735c27.521-25.273,57.405-47.633,87.762-69.379c18.694-13.392,36.582-27.832,53.685-43.229c16.488-14.843,32.362-30.29,45.838-47.98c17.679-23.209,29.727-48.903,33.311-78.143c2.609-21.289,1.243-42.266-6.131-62.537c-7.747-21.296-22.185-36.557-43.075-45.389c-15.588-6.591-31.878-10.42-48.873-10.886C380.353,142.965,379.902,142.813,379.493,142.51z"/><path fill-rule="evenodd" clip-rule="evenodd" fill="#F3E09F" d="M379.493,142.51c27.084,0.602,52.521,6.219,73.538,24.731c14.061,12.384,21.812,28.467,25.311,46.509c9.661,49.813-4.313,93.438-35.788,132.132c-24.233,29.791-53.419,54.331-83.834,77.435c-25.05,19.028-50.844,37.057-75.198,57.026c-45.073,36.958-83.462,79.283-108.655,132.504c-12.787,27.013-20.634,55.33-23.55,85.103c-2.817,28.76-1.151,57.568-1.595,86.353c-10.168,0.631-10.168,0.631-10.168-9.529c0-17.494-0.203-34.991,0.04-52.482c0.639-46.13,12.512-89.166,35.469-129.253c13.788-24.076,30.077-46.176,48.8-66.632c18.825-20.567,40.102-38.348,61.636-55.872c26.817-21.823,56.026-40.501,82.677-62.531c27.596-22.812,54.442-46.441,74.813-76.343c16.729-24.556,26.769-51.343,27.745-81.402c0.62-19.128-0.945-37.704-8.494-55.515c-8.801-20.764-24.103-34.706-44.739-43.117c-11.308-4.609-23.072-7.35-35.267-8.186C381.229,143.371,380.225,143.357,379.493,142.51z"/><path fill-rule="evenodd" clip-rule="evenodd" fill="#F3E09F" d="M368.559,16.558c-7.21,0.945-14.418,1.916-21.633,2.83c-33.484,4.24-65.852,12.773-97.127,25.425c-21.803,8.82-42.625,19.561-62.581,32.028c-2.985,1.865-3.864,3.857-3.855,7.191c0.129,45.147,0.131,90.294,0.164,135.441c-1.955-1.703-0.208-4.204-1.391-6.214c-3.216,0.998-4.44,4.924-7.99,5.597c-1.313-1.21-0.663-2.792-0.664-4.155c-0.042-43.633,0.01-87.267-0.119-130.899c-0.01-3.448,1.11-5.439,3.944-7.208c37.552-23.438,77.789-40.454,120.935-50.402c17.932-4.135,36.043-7.308,54.43-8.825C357.971,16.929,363.227,16.174,368.559,16.558z"/>',
  },
})
Icon.register({
  count_down_s1_3: {
    width: 800,
    height: 800,
    raw:
      '<path fill-rule="evenodd" clip-rule="evenodd" fill="#FEC500" d="M363.483,128.541c-54.459-4.671-103.798,10.539-149.818,38.606c-5.094,3.107-10.132,6.306-15.196,9.462c-3.322-9.779-2.443-19.907-2.45-29.954c-0.022-30.474,0.149-60.949-0.129-91.421c-0.063-6.844,2.525-10.544,8.61-13.412c30.442-14.351,62.31-23.892,95.469-29.294c7.174-1.168,14.37-2.192,21.6-2.963c22.718-2.446,45.502-3.521,68.346-3.042c48.133,1.011,94.744,8.873,137.859,31.62c33.298,17.567,60.799,41.262,77.974,75.544c14.326,28.597,18.638,59.122,17.327,90.687c-1.028,24.783-5.183,48.965-14.859,71.965c-17.306,41.135-47.987,68.836-87.901,87.184c-14.087,6.476-28.691,11.514-43.674,15.495c-1.384,0.368-3.978,0.029-3.587,2.472c0.29,1.808,2.499,1.145,3.835,1.328c25.545,3.499,50.366,9.579,73.359,21.575c48.932,25.527,82.846,63.956,92.648,118.92c14.456,81.061-10.361,167.851-97.119,220.008c-35.917,21.593-74.957,33.99-116.171,40.181c-46.586,6.997-93.279,6.338-140.022,1.912c-21.945-2.365-43.664-6.034-64.944-11.948c-17.838-4.958-35.192-11.23-51.585-19.994c-3.52-1.881-5.067-4.394-5.066-8.34c0.011-40.988,0.004-81.977,0.031-122.966c0.001-2.003,0.014-4.026,1.476-5.659c0.08-1.491,0.159-2.982,0.279-5.24c58.955,42.927,124.694,60.377,196.701,55.17c18.823-1.717,37.01-6.143,54.442-13.391c20.474-8.514,37.669-21.153,49.552-40.344c6.615-10.683,10.291-22.315,12.242-34.621c2.921-18.421,2.074-36.612-4.189-54.23c-7.79-21.913-23.203-37.418-43.018-48.806c-22.256-12.791-46.631-19.129-71.761-23.104c-3.738-0.591-7.533-0.666-11.235-1.477c-12.092-1.424-24.229-2.005-36.4-2.013c-22.492-0.014-44.985-0.047-67.479-0.072c-1.861-1.839-1.742-4.232-1.744-6.543c-0.039-36.217-0.041-72.434,0.003-108.65c0.003-2.304-0.149-4.712,1.799-6.506c22.646-0.039,45.293-0.052,67.938-0.13c10.33-0.035,20.625-0.721,30.874-2.034c19.702-2.319,38.882-6.789,57.237-14.391c18.277-7.569,33.657-18.806,44.075-36.032c7.396-12.23,11.022-25.699,12.078-39.786c1.255-16.735-0.073-33.282-5.825-49.236c-8.438-23.403-25.322-38.209-48.123-46.78C392.935,133.024,378.422,129.775,363.483,128.541z"/><path fill-rule="evenodd" clip-rule="evenodd" fill="#F3E09F" d="M352.509,450.464c20.412,0.973,40.136,5.376,59.334,12.183c15.653,5.55,30.102,13.294,42.717,24.264c16.36,14.228,26.269,32.104,29.588,53.427c3.038,19.524,1.762,38.867-5.04,57.549c-6.514,17.895-17.91,32.176-33.514,43.054c-23.76,16.564-50.459,24.091-79.118,25.496c9.294-2.283,18.864-3.148,28.093-5.868c21.386-6.305,40.894-15.779,56.53-32.136c13.802-14.438,21.535-31.808,24.229-51.405c2.611-18.997,1.632-37.742-5.424-55.867c-8.283-21.279-23.538-36.261-42.933-47.411c-22.238-12.786-46.554-19.039-71.779-22.275C354.199,451.346,353.182,451.394,352.509,450.464z"/><path fill-rule="evenodd" clip-rule="evenodd" fill="#F3E09F" d="M363.483,128.541c27.021,1.237,52.325,7.544,73.14,25.984c9.897,8.768,16.574,19.918,20.558,32.526c6.446,20.401,7.255,41.194,2.84,62.041c-4.854,22.925-17.446,40.822-37.346,53.194c-22.962,14.276-48.46,20.418-75.175,22.229c9.18-1.983,18.432-3.687,27.524-6.008c19.579-5,37.819-12.95,52.688-27.014c12.15-11.493,19.927-25.776,23.251-42.154c4.65-22.906,3.824-45.584-4.802-67.589c-8.623-21.996-25.093-35.916-46.624-44.308c-11.021-4.295-22.467-7.086-34.304-8.084C364.631,129.309,364.065,128.824,363.483,128.541z"/><path fill-rule="evenodd" clip-rule="evenodd" fill="#F3E0A0" d="M169.496,616.506c0.025,42.647,0.123,85.296-0.035,127.943c-0.016,4.313,1.357,6.359,5.153,8.285c30.591,15.518,63.185,24.349,96.859,29.795c6.034,0.977,12.222,1.003,18.109,2.885c-13.001-0.102-25.695-2.66-38.411-4.93c-29.929-5.342-58.745-14.126-85.926-27.983c-3.347-1.706-4.836-3.466-4.819-7.5c0.18-42.643,0.111-85.287,0.111-127.932c0-1.627,0-3.254,0-5.238C164.394,612.488,166.083,616.199,169.496,616.506z"/><path fill-rule="evenodd" clip-rule="evenodd" fill="#F3E0A0" d="M321.568,9.565c-11.068,1.97-22.177,3.738-33.197,5.946c-29.953,6.002-58.638,15.797-86.124,29.149c-3.226,1.567-4.62,3.457-4.606,7.227c0.141,38.1,0.067,76.199,0.119,114.3c0.004,3.474,0.462,6.947,0.709,10.422c-2.242-0.805-0.124-3.173-1.555-4.274c-0.145,0.011-0.323-0.011-0.475,0.042c-2.565,0.906-4.629,4.79-7.167,3.208c-1.909-1.191-0.63-4.978-0.634-7.595c-0.048-38.324,0.042-76.65-0.133-114.975c-0.02-4.498,1.437-6.858,5.479-8.787c32.616-15.562,66.689-26.342,102.43-31.771C304.761,11.19,313.027,9.159,321.568,9.565z"/><path fill-rule="evenodd" clip-rule="evenodd" fill="#F3E09F" d="M248.688,326.68c-0.095,1.989-0.273,3.978-0.274,5.967c-0.017,36.753-0.015,73.506-0.002,110.258c0.001,1.825,0.143,3.65,0.219,5.475c-11.361,0.194-9.106,0.871-9.135-8.646c-0.105-34.828-0.105-69.658,0-104.487C239.524,325.901,237.197,326.393,248.688,326.68z"/>',
  },
})
Icon.register({
  count_down_s1_GO: {
    width: 800,
    height: 800,
    raw:
      '<path fill-rule="evenodd" clip-rule="evenodd" fill="#FEC500" d="M209.452,257.462c-39.873,11.818-67.943,37.622-83.141,75.578c-19.477,48.643-18.806,98.281,2.981,146.11c17.671,38.793,49.668,60.26,91.734,66.359c24.82,3.6,49.465,2.576,73.625-4.831c1.27-0.39,2.508-0.883,3.76-1.327c5.479-3.412,5.583-8.723,5.545-14.404c-0.159-23.327-0.055-46.656-0.073-69.984c-0.006-7.559-0.342-7.942-7.854-7.956c-23.661-0.042-47.323-0.004-70.984-0.021c-8.208-0.006-9.117-0.885-9.138-9.214c-0.047-19.162,0.009-38.325-0.039-57.487c-0.008-3.368,0.561-6.576,1.725-9.708c59.31-0.025,118.62-0.02,177.929-0.154c3.435-0.008,4.04,1.151,4.036,4.255c-0.078,69.973-0.082,139.945,0.014,209.919c0.005,3.144-1.112,4.715-3.765,6.146c-17.646,9.527-36.13,16.971-55.327,22.695c-24.765,7.224-50.043,11.052-75.797,12.658c-35.868,2.238-71.229-0.071-105.808-10.152c-28.981-8.449-55.112-22.24-77.926-42.35c-32.194-28.378-51.505-64.03-60.977-105.423c-3.458-15.115-5.087-30.454-6.042-45.97C11.787,387.356,15.648,353.378,27,320.385c9.598-27.896,24.307-52.791,44.505-74.507c13.126-14.113,27.794-26.143,44.092-36.299c30.958-19.006,64.623-30.226,100.568-34.755c26.196-3.301,52.485-3.12,78.735-1.02c27.939,2.236,55.34,7.412,81.825,16.865c2.013,0.718,3.864,1.134,3.85,4.198c-0.132,28.297-0.077,56.595-0.077,85.005c-2.528,0.136-3.979-1.84-5.99-2.4c-2.361-0.192-4.466-1.124-6.568-2.149c-24.493-11.938-50.425-18.653-77.46-21.507c-17.222-1.817-34.448-2.622-51.734-1.317C228.833,253.248,219.051,254.834,209.452,257.462z"/><path fill-rule="evenodd" clip-rule="evenodd" fill="#FEC500" d="M529.595,322.579c37.501-21.905,78.272-27.558,120.836-24.333c30.263,2.293,58.568,10.542,83.477,28.366c32.946,23.575,51.984,55.88,58.646,95.63c5.389,32.161,4.352,64.163-5.448,95.299c-16.386,52.065-51.78,85.217-103.743,101.063c-0.953,0.291-1.901,0.6-2.852,0.899c-21.419,6.153-43.344,8.37-65.499,7.26c-31.517-1.579-61.729-8.341-88.647-25.781c-36.483-23.638-57.485-57.588-64.965-100.019c-5.773-32.754-4.801-65.373,4.978-97.333C477.08,368.645,497.662,341.126,529.595,322.579z M684.643,524.334c4.486-9.207,7.527-18.857,9.12-28.951c2.285-14.475,3.094-28.981,2.28-43.684c-0.724-13.084-2.449-25.855-6.95-38.147c-8.516-23.255-24.526-37.865-49.242-42.555c-13.297-2.523-26.377-2.32-39.38,1.482c-21.04,7.2-33.654,22.713-40.833,42.85c-9.109,25.55-9.024,51.878-4.444,78.287c2.959,17.065,8.767,33.23,22.466,44.529c25.025,20.642,53.297,21.356,82.773,11.194C671.278,543.692,679.286,535.328,684.643,524.334z"/><path fill-rule="evenodd" clip-rule="evenodd" fill="#F3E09F" d="M115.597,209.579c-6.851,5.233-13.877,10.252-20.522,15.734c-39.156,32.308-64.05,73.363-74.545,123.008c-6.128,28.989-7.176,58.265-4.291,87.772c2.846,29.106,9.816,56.993,23.528,82.913c22.51,42.549,56.78,71.997,101.4,89.538c24.802,9.75,50.673,14.508,77.233,16.141c40.46,2.487,80.349-0.715,119.605-11.043c0.788-0.208,1.648-0.143,2.476-0.204c-9.437,4.254-19.583,5.926-29.594,8.049c-31.404,6.66-63.265,8.293-95.173,6.772c-36.664-1.746-72.14-9.408-104.701-27.16c-33.514-18.271-59.698-44.057-77.741-77.936c-13.548-25.439-20.686-52.663-23.891-81.15c-1.603-14.246-2.293-28.508-1.765-42.787c1.653-44.65,12.313-86.646,37.611-124.164c17.541-26.013,39.794-47.147,66.271-63.893C112.746,210.382,113.96,209.41,115.597,209.579z"/><path fill-rule="evenodd" clip-rule="evenodd" fill="#F3E09F" d="M529.595,322.579c-7.183,5.62-14.817,10.641-21.569,16.839c-24.985,22.939-39.479,51.568-45.286,84.634c-5.35,30.464-5.16,61.018,3.155,90.939c15.435,55.542,52.066,90.073,107.473,104.37c34.174,8.817,68.758,8.279,103.214,0.635c1.282-0.285,2.617-0.333,3.929-0.492c-8.752,4.374-18.414,5.214-27.813,6.865c-15.625,2.745-31.489,2.825-47.242,1.969c-44.378-2.413-83.904-16.428-114.662-50.123c-19.026-20.842-30.244-45.672-35.04-73.195c-6.13-35.18-5.003-70.133,6.819-104.21c11.655-33.6,33.461-58.93,63.781-77.147C527.307,323.09,528.509,322.931,529.595,322.579z"/><path fill-rule="evenodd" clip-rule="evenodd" fill="#F3E0A0" d="M217.593,370.577c-0.088,23.827-0.111,47.654-0.327,71.479c-0.038,4.165,2.567,3.436,4.972,3.437c25.831,0.011,51.663,0.139,77.491-0.11c4.813-0.047,6.112,1.17,6.065,6.03c-0.254,25.995-0.333,51.996,0.041,77.988c0.085,5.957-1.646,9.084-7.424,9.95c0.047-28.954,0.022-57.909,0.221-86.862c0.028-4.104-2.094-4.025-4.993-4.02c-26.127,0.05-52.255-0.095-78.379,0.144c-4.717,0.043-5.79-1.405-5.744-5.884c0.219-21.465,0.129-42.934,0.063-64.4c-0.009-2.977,0.084-5.604,4.094-4.789C216.319,374.079,216.12,371.324,217.593,370.577z"/><path fill-rule="evenodd" clip-rule="evenodd" fill="#F3E09F" d="M209.452,257.462c8.094-3.751,16.862-4.95,25.532-5.747c36.823-3.383,72.95,0.437,108.146,11.938c10.887,3.557,21.539,7.806,31.377,13.82c-1.862,1.324-0.437,3.494-1.604,5.616c-6.114-2.594-11.77-6.282-17.905-8.966c-20.502-8.969-41.802-14.79-63.972-17.7c-14.983-1.968-29.992-3.052-45.109-2.905c-10.649,0.104-21.131,1.41-31.549,3.49C212.764,257.328,211.093,257.319,209.452,257.462z"/><path fill-rule="evenodd" clip-rule="evenodd" fill="#F3E09F" d="M660.433,549.34c11.658-8.004,20.521-18.301,25.798-31.523c4.527-11.347,7.097-23.242,8.052-35.308c1.601-20.214,1.563-40.47-3.828-60.263c-4.92-18.068-13.622-33.706-30.797-42.891c-14.303-7.647-29.839-9.747-45.88-8.426c-4.447,0.367-8.871,1.025-13.307,1.55c10.714-5.01,22.181-5.395,33.612-4.812c19.268,0.983,36.55,6.896,49.302,22.388c9.811,11.917,14.595,26.07,16.82,41.014c3.731,25.045,3.281,50.027-3.791,74.535c-5.15,17.85-14.485,32.775-31.436,41.871C663.529,548.254,662.202,549.339,660.433,549.34z"/>',
  },
})
//大屏幕通知
Icon.register({
  tomlive_note: {
    width: 1024,
    height: 1024,
    raw:
      '<path d="M622.592 81.92c-13.312 0-25.6 4.096-36.864 11.264l-348.16 230.4c-14.336 9.216-31.744 15.36-49.152 15.36h-69.632C80.896 337.92 51.2 367.616 51.2 404.48v202.752c0 36.864 29.696 66.56 66.56 66.56h69.632c17.408 0 34.816 5.12 49.152 15.36l349.184 230.4c11.264 7.168 23.552 11.264 36.864 11.264 36.864 0 66.56-29.696 66.56-66.56V148.48c1.024-36.864-29.696-66.56-66.56-66.56zM102.4 607.232V404.48c0-8.192 7.168-15.36 15.36-15.36h66.56v233.472h-66.56c-8.192 0-15.36-7.168-15.36-15.36z m535.552 256c0 9.216-8.192 15.36-15.36 15.36-3.072 0-6.144-1.024-8.192-3.072L265.216 646.144c-8.192-5.12-16.384-10.24-25.6-13.312V378.88c9.216-3.072 17.408-8.192 25.6-13.312l349.184-230.4c3.072-2.048 5.12-3.072 8.192-3.072 7.168 0 15.36 6.144 15.36 15.36v715.776z m159.744-477.184c-11.264-9.216-27.648-7.168-35.84 4.096-9.216 11.264-7.168 27.648 4.096 35.84 27.648 22.528 44.032 56.32 44.032 92.16 0 34.816-15.36 67.584-41.984 90.112-11.264 9.216-12.288 25.6-3.072 35.84 5.12 6.144 12.288 9.216 19.456 9.216 6.144 0 11.264-2.048 16.384-6.144 38.912-31.744 60.416-79.872 60.416-130.048s-22.528-98.304-63.488-131.072z" fill="" p-id="1862"/><path d="M867.328 271.36c-11.264-8.192-27.648-5.12-35.84 7.168-8.192 11.264-5.12 27.648 7.168 35.84 66.56 45.056 106.496 119.808 106.496 199.68 0 84.992-43.008 161.792-115.712 205.824-12.288 7.168-16.384 23.552-8.192 34.816 5.12 8.192 13.312 12.288 21.504 12.288 4.096 0 9.216-1.024 13.312-4.096 88.064-53.248 140.288-146.432 140.288-249.856-1.024-96.256-49.152-186.368-129.024-241.664z" fill="" p-id="1863"/>',
  },
})

// 大屏幕活动图标
// 弹幕
Icon.register({
  wall_danmu: {
    width: 34,
    height: 34,
    raw:
      '<circle cx="17" cy="17" r="17"/><path fill="#FFFFFF" d="M27.521,20.444c-1.913-0.127-3.73-0.159-5.451-0.097c0-0.7,0-1.37,0-2.009  c1.467-0.126,2.933-0.191,4.399-0.191l-0.478-1.053c-0.064,0-0.16,0-0.286,0c0.444-1.722,0.7-3.093,0.764-4.113  c0.063-1.53-0.86-2.55-2.773-3.062c0.319-0.574,0.605-1.242,0.861-2.009c0.444-0.7,0.223-1.274-0.671-1.721  c-0.83-0.381-1.402-0.189-1.721,0.575c-0.128,0.256-0.286,0.606-0.479,1.053c-0.319,0.83-0.573,1.499-0.765,2.009  c-0.638,0-1.212,0.064-1.721,0.192c-0.063,0-0.128,0-0.192,0c-0.065-0.192-0.129-0.511-0.192-0.957  c-0.128-0.574-0.223-0.956-0.285-1.148c-0.448-0.893-1.183-1.307-2.201-1.243c-0.639,0.255-0.829,0.829-0.574,1.722  c0.254,0.956,0.414,1.785,0.478,2.487c0,0.063-0.033,0.096-0.096,0.096c-0.256-0.255-0.638-0.383-1.148-0.383  c-0.7,0.064-1.051,0.733-1.051,2.008c0.192,3.382,0.318,5.931,0.382,7.653c0.575-0.127,1.531-0.192,2.87-0.192  c0-0.255,0-0.479,0-0.67c0.574-0.19,1.244-0.383,2.009-0.573c0,0.512,0,1.053,0,1.627c-1.786,0.191-3.381,0.478-4.782,0.86  c-0.639,0.256-1.116,0.606-1.435,1.053c0.063-0.127,0.126-0.319,0.191-0.574c0.255-1.021,0.415-1.723,0.479-2.105  c0.127-1.657-0.606-2.646-2.2-2.965c-0.702-0.127-1.468-0.192-2.295-0.192c0.317-0.891,0.54-1.465,0.669-1.722  c0.063-0.254,0-0.478-0.192-0.67c1.339-0.444,2.741-0.765,4.208-0.956l-0.479-1.052h-0.382c0.063-0.254,0.222-0.604,0.479-1.052  c0.191-0.511,0.318-0.861,0.382-1.053c0.318-0.892,0.032-1.625-0.86-2.199C11.644,7.054,10.049,6.926,8.2,7.436  C7.179,8.01,6.797,8.712,7.051,9.541c0.382,0.512,0.923,0.574,1.626,0.191c0.701-0.382,1.148-0.574,1.339-0.574  c0.637-0.126,0.893,0.16,0.765,0.861c-0.51,0.955-0.859,1.721-1.051,2.295c-0.765,0.063-1.499,0.19-2.2,0.383  c-0.83,0.255-1.212,0.764-1.148,1.53c0,0.319,0.127,0.542,0.382,0.67C6.382,15.917,6.126,17.065,6,18.341  c0,0.829,0.445,1.052,1.339,0.669c1.083-0.382,1.817-0.604,2.2-0.669c0.764-0.127,1.148,0.159,1.148,0.86  c-0.192,2.425-1.18,5.102-2.965,8.034c0.957-0.256,2.009-0.256,3.156,0c0.701-1.276,1.307-2.678,1.817-4.208  c0,0.128,0.032,0.191,0.096,0.191c0.318,0.702,1.148,0.86,2.487,0.479c1.083-0.383,2.392-0.765,3.922-1.148  c0.063,0.894,0.063,2.104,0,3.635c0,0.828,0,1.435,0,1.816c0.829-0.192,1.817-0.192,2.966,0c0-2.104,0-4.08,0-5.93  c1.785-0.318,3.73-0.414,5.835-0.286L27.521,20.444L27.521,20.444z M16.809,13.079c0.765-0.574,1.497-0.988,2.199-1.243  c0,0.064,0,0.128,0,0.192c-0.063,0.191-0.063,0.319,0,0.382c0,0.383,0,0.83,0,1.339c-0.383,0-0.86,0.064-1.436,0.192  c-0.319,0.063-0.542,0.096-0.67,0.096C16.904,13.717,16.871,13.398,16.809,13.079L16.809,13.079z M18.147,17.002  c-0.513,0-0.895,0.033-1.148,0.096c0-0.318,0-0.605,0-0.861c0.636-0.317,1.339-0.573,2.104-0.765c0,0.446,0.032,0.925,0.097,1.436  C18.944,16.905,18.594,16.938,18.147,17.002L18.147,17.002z M23.408,14.992c-0.128,0.895-0.256,1.499-0.382,1.817h-0.957  c0-0.7,0-1.339,0-1.913c0.51-0.063,0.957-0.096,1.339-0.096C23.408,14.866,23.408,14.93,23.408,14.992L23.408,14.992z   M21.208,11.357c0.192-0.063,0.445-0.096,0.766-0.096c1.082,0,1.656,0.479,1.721,1.436c0,0.319-0.033,0.573-0.096,0.764h-1.627  c0-0.317,0-0.67,0-1.052C21.974,11.836,21.717,11.486,21.208,11.357L21.208,11.357z"/>',
  },
})
// 禁止图标
Icon.register({
  wall_ban: {
    width: 360,
    height: 360,
    raw:
      '<path d="M180,0C80.748,0,0,80.748,0,180c0,99.252,80.748,180,180,180c99.252,0,180-80.748,180-180C360,80.748,279.252,0,180,0z   M40,180c0-77.196,62.804-140,140-140c33.28,0,63.88,11.68,87.931,31.146L71.146,267.932C51.68,243.88,40,213.28,40,180z M180,320  c-29.512,0-56.914-9.187-79.514-24.839L295.16,100.486C310.813,123.086,320,150.488,320,180C320,257.196,257.196,320,180,320z"/>',
  },
})
// 嘉宾
Icon.register({
  wall_guest: {
    width: 1024,
    height: 1024,
    raw: `<rect x="103.3" y="151.5" fill="none" width="810.88" height="716.88"/>
    <path  d="M512.007,0C229.221,0,0,229.26,0,512c0,282.738,229.223,512,512.009,512C794.797,1024,1024,794.771,1024,512
      C1024,229.23,794.781,0,512.007,0z M266.783,637.325v32.564c0,11.637-9.144,21.053-20.44,21.053
      c-11.295,0-20.436-9.415-20.436-21.053v-32.564c0-64.248,34.689-121.032,87.878-149.421c-16.726-18.997-26.564-43.976-26.564-70.62
      c0-48.825,32.152-90.93,78.196-102.363c10.919-2.631,21.997,4.237,24.673,15.542c2.632,11.308-4.111,22.68-15.092,25.415
      c-27.621,6.844-46.899,32.09-46.899,61.406c0,24.917,14.288,47.534,36.404,57.689c2.436,1.09,4.57,2.611,6.428,4.605
      c0.198,0.202,0.416,0.35,0.597,0.555c1.257,1.458,2.177,3.104,3.016,4.913c0.439,0.883,0.877,1.728,1.195,2.669
      c0.144,0.496,0.48,0.843,0.6,1.34c0.2,0.861,0,1.686,0.08,2.551c0.221,1.624,0.32,3.205,0.159,4.87
      c-0.099,0.843-0.321,1.605-0.521,2.407c-0.278,1.314-0.278,2.673-0.837,3.946c-2.954,6.925-9.122,11.182-15.905,12.17
      C304.783,529.143,266.783,579.104,266.783,637.325z M716.419,733.044c0,11.634-9.139,21.057-20.436,21.057H328.098
      c-11.298,0-20.438-9.418-20.438-21.057V690.94c0-88.564,53.468-164.409,128.835-195.431c-28.603-23.149-47.083-59.003-47.083-99.301
      c0-69.651,55.005-126.311,122.627-126.311c67.624,0,122.632,56.663,122.632,126.311c0,40.298-18.468,76.151-47.085,99.301
      c75.385,31.021,128.836,106.843,128.834,195.431V733.044z M777.654,690.942c-11.295,0-20.44-9.419-20.44-21.05v-32.567
      c0-58.227-38-108.179-92.528-122.327c-6.783-0.982-12.952-5.243-15.908-12.167c-0.559-1.273-0.559-2.632-0.837-3.949
      c-0.201-0.799-0.417-1.562-0.519-2.4c-0.159-1.668-0.059-3.25,0.162-4.874c0.08-0.863-0.121-1.689,0.08-2.55
      c0.117-0.492,0.46-0.864,0.595-1.337c0.301-0.924,0.757-1.747,1.178-2.631c0.857-1.83,1.776-3.515,3.054-4.974
      c0.161-0.188,0.362-0.309,0.52-0.497c1.878-2.013,4.054-3.555,6.526-4.664c22.095-10.18,36.364-32.753,36.364-57.669
      c0-28.534-18.659-53.637-45.366-61.017c-10.897-3.026-17.367-14.558-14.433-25.806c2.958-11.223,14.174-17.863,25.054-14.863
      c44.521,12.316,75.621,54.134,75.621,101.686c0,26.642-9.837,51.622-26.561,70.619c53.189,28.389,87.876,85.173,87.876,149.421
      v32.566h0.002C798.094,681.523,788.948,690.942,777.654,690.942z"/>
    </svg>`,
  },
})
// 消息
Icon.register({
  wall_msg: {
    width: 1024,
    height: 1024,
    raw: `<g><path d="M529.553,379.72c-4.664-4.651-10.976-7.276-17.554-7.276c-6.59,0-12.903,2.625-17.552,7.276l-35.093,35.093
      c-9.695,9.695-16.767,27.76-7.073,37.44h39.805v99.548c-0.163,7.232,3.588,13.97,9.827,17.642c6.226,3.677,13.934,3.677,20.176,0
      c6.242-3.672,9.977-10.409,9.795-17.642v-99.52h39.836c9.681-9.709,2.622-27.772-7.086-37.467L529.553,379.72z"/>
    <path  d="M512.008,0C229.221,0,0,229.26,0,512c0,282.738,229.221,512,512.009,512S1024,794.771,1024,512
      C1024,229.232,794.781,0,512.008,0z M750.873,631.438c0,10.555-4.199,20.704-11.666,28.166
      c-7.463,7.468-17.61,11.633-28.164,11.633H571.896l79.604,79.637H372.459l79.618-79.605H312.929
      c-10.556,0-20.674-4.196-28.152-11.666c-7.466-7.462-11.65-17.61-11.65-28.166V312.929c0-21.985,17.818-39.802,39.802-39.802
      h398.114c10.554,0,20.701,4.184,28.164,11.65c7.465,7.478,11.666,17.596,11.666,28.152V631.438z"/></g>`,
  },
})
// 提问
Icon.register({
  wall_question: {
    width: 1024,
    height: 1024,
    raw: `<path  d="M512.009,0C229.226,0,0,229.226,0,512.011C0,794.775,229.226,1024,512.009,1024
    C794.773,1024,1024,794.775,1024,512.011C1024.002,229.226,794.773,0,512.009,0z M500.62,748.486
    c-25.046,0-45.351-20.307-45.351-45.354s20.304-45.35,45.351-45.35c25.067,0,45.372,20.303,45.372,45.35
    S525.688,748.486,500.62,748.486z M541.571,607.873v1.678c0,17.004-14.057,30.791-31.397,30.791
    c-17.359,0-31.417-13.787-31.417-30.791v-32.604c0-17.006,14.059-30.793,31.417-30.793c1.382,0,2.73,0.115,4.066,0.286l0.021-0.562
    c55.796-1.205,100.66-46.778,100.66-102.858c0-56.836-46.073-102.964-102.914-102.964c-56.855,0-102.933,46.127-102.933,102.964
    c0,4.175,0.277,8.28,0.761,12.324l-0.61,0.146c1.403,3.672,2.205,7.643,2.205,11.81c0,18.303-14.836,33.143-33.14,33.143
    c-18.303,0-33.14-14.84-33.14-33.143c0-1.83,0.187-3.61,0.472-5.358c-0.703-6.21-1.093-12.513-1.093-18.909
    c0-92.479,74.967-167.516,167.493-167.516c92.477,0,167.444,75.038,167.444,167.516C679.47,525.424,619.955,593.918,541.571,607.873
    z"/>`,
  },
})
// 图片
Icon.register({
  wall_pic: {
    width: 1024,
    height: 1024,
    raw: `<g>
    <path d="M448.085,438.729c13.491,0,24.43-10.936,24.43-24.427c0-8.729-4.655-16.788-12.222-21.16
      c-7.55-4.357-16.861-4.357-24.412,0c-7.565,4.372-12.222,12.432-12.222,21.16C423.658,427.792,434.598,438.729,448.085,438.729z"/>
    <polygon points="461.115,594.641 438.729,560.857 389.873,634.127 634.127,634.127 536.414,463.143 	"/>
    <path  d="M512.008,0C229.221,0,0,229.26,0,512c0,282.738,229.221,512,512.009,512S1024,794.771,1024,512
      C1024,229.232,794.781,0,512.008,0z M756.252,674.836c0,43.217-33.693,78.611-76.283,81.24l-5.133,0.176h-325.67
      c-43.231,0-78.598-33.693-81.255-76.283l-0.163-5.133v-325.67c0-43.231,33.695-78.597,76.27-81.255l5.148-0.163h325.67
      c43.215,0,78.611,33.695,81.238,76.27l0.178,5.148V674.836z"/>
  </g>`,
  },
})
// 签到
Icon.register({
  wall_sign: {
    width: 1024,
    height: 1024,
    raw: `<g>
    <path  d="M555.676,644.209h128.866c3.979,0,7.186-4.496,7.186-10.047c0-5.553-3.206-10.08-7.186-10.08H555.676
      c-3.981,0-7.154,4.527-7.154,10.08C548.521,639.713,551.693,644.209,555.676,644.209z"/>
    <path  d="M555.676,562.896h102.014c3.979,0,7.188-4.527,7.188-10.078c0-5.552-3.208-10.05-7.188-10.05H555.676
      c-3.981,0-7.154,4.498-7.154,10.05C548.521,558.368,551.693,562.896,555.676,562.896z"/>
    <path  d="M556.553,479.016h75.159c3.982,0,7.154-4.498,7.154-10.048c0-5.553-3.172-10.048-7.154-10.048h-75.159
      c-3.98,0-7.151,4.495-7.151,10.048C549.4,474.519,552.571,479.016,556.553,479.016z"/>
    <path d="M512.008,0C229.22,0,0,229.26,0,512c0,282.738,229.221,512,512.009,512C794.797,1024,1024,794.771,1024,512
      C1024,229.232,794.781,0,512.008,0z M740.271,719.912c0,22.055-12.753,39.949-28.473,39.949h-90.816H405.354h-90.849
      c-15.687,0-28.455-17.896-28.455-39.949V342.395h454.222V719.912L740.271,719.912z M774.208,303.14h-526.11
      c-7.336,0-13.308-8.418-13.308-18.796c0-10.411,5.972-18.828,13.308-18.828h526.11c7.321,0,13.292,8.417,13.292,18.828
      C787.5,294.722,781.529,303.14,774.208,303.14z"/>
    <path  d="M342.886,639.502h152.656c5.381,0,9.753-4.707,9.753-10.5V464.623c0-5.792-4.371-10.5-9.753-10.5H342.886
      c-5.38,0-9.752,4.708-9.752,10.5v164.379C333.135,634.795,337.508,639.502,342.886,639.502z M362.959,608.758
      c1.03-3.139,3.299-6.549,3.912-7.362c1.112-1.511,11.747-11.437,17.275-14.15c2.018-0.996,13.978-5.492,17.341-8.781
      c3.383-3.289,4.685-9.836,0.65-14.119c-4.038-4.285-11.349-22.358-11.349-22.358s-7.819-33.01,8.826-46.375
      c5.991-5.099,12.444-7.392,17.656-7.392c6.052,0,12.59,1.538,16.161,3.562c6.433,3.648,10.3,8.898,13.515,16.594
      c3.869,9.292,2.963,22.417,1.282,29.507c-1.745,7.424-6.22,19.098-12.612,27.066c-6.431,7.994,0.274,14.27,1.957,14.901
      c7.736,2.898,21.459,9.566,28.458,16.021c3.406,3.106,7.714,7.906,9.289,12.553c0.65,1.961,0.401,3.894,0.507,5.34l-113.437,0.271
      C362.392,614.035,362.055,611.502,362.959,608.758z"/>
  </g>`,
  },
})
// LOGO
Icon.register({
  wall_logo: {
    width: 1024,
    height: 1024,
    raw: `<path  d="M512.008,0C229.22,0,0,229.26,0,512c0,282.738,229.221,512,512.009,512C794.797,1024,1024,794.771,1024,512
    C1024,229.232,794.781,0,512.008,0z M790.664,500.584L511.217,753.177l-279.45-252.592V369.442L310.5,269.121h98.754l101.963,81.781
    l101.959-81.781h98.758l78.729,100.322L790.664,500.584L790.664,500.584z"/>`,
  },
})
// 3D
Icon.register({
  wall_threed: {
    width: 1024,
    height: 1024,
    raw: `<g>
    <polygon  points="358.953,481.992 309.67,454.448 291.714,459.251 363.755,499.948 430.256,384.783 412.3,389.586"/>
    <path  d="M512.008,0C229.22,0,0,229.26,0,512c0,282.738,229.221,512,512.009,512C794.797,1024,1024,794.771,1024,512
      C1024,229.232,794.781,0,512.008,0z M238.734,363.866l258.877-124.57h0.017v297.77l-258.894,116V363.866z M512.137,786.471
      L256.623,671.909l249.773-111.885h11.497l249.74,111.885L512.137,786.471z M785.505,653.065L526.596,537.031V239.279
      l258.909,124.586V653.065z"/>
  </g>`,
  },
})
// 倒计时
Icon.register({
  wall_countdown: {
    width: 1024,
    height: 1024,
    raw: `<g>
    <path  d="M512.008,0C229.22,0,0,229.26,0,512c0,282.738,229.221,512,512.009,512C794.797,1024,1024,794.771,1024,512
      C1024,229.232,794.781,0,512.008,0z M512.273,782.152c-149.194,0-270.159-120.95-270.159-270.162
      c0-149.207,120.965-270.159,270.159-270.159c149.208,0,270.16,120.952,270.16,270.159
      C782.434,661.202,661.481,782.152,512.273,782.152z"/>
    <path  d="M636.951,511.99H533.049V366.524c0-11.473-9.301-20.774-20.775-20.774
      c-11.474,0-20.774,9.301-20.774,20.774v166.244c0,11.475,9.301,20.776,20.774,20.776h124.678c11.504,0,20.774-9.303,20.774-20.776
      C657.727,521.295,648.455,511.99,636.951,511.99z"/>
  </g>`,
  },
})
// 签约墙
Icon.register({
  wall_contract: {
    width: 1024,
    height: 1024,
    raw: `
          <g>
            <path class="st1" d="M646.8,367.9h61l-122-123.3v61.6C585.8,340.3,613,367.9,646.8,367.9z"/>
            <polygon class="st1" points="585.8,244.5 585.7,244.5 585.8,244.6 	"/>
            <path class="st1" d="M495.8,491.3h-146c-12.2,0-21.9,9.2-21.9,20.7c0,11.5,9.8,20.7,21.9,20.7h146c12.1,0,21.9-9.2,21.9-20.7
              C517.7,500.6,507.9,491.3,495.8,491.3z"/>
            <path class="st1" d="M350.1,450.2h253.7c12.4,0,22.4-9.2,22.4-20.7c0-11.5-10-20.7-22.4-20.7H350.1c-12.4,0-22.4,9.2-22.4,20.7
              C327.7,441,337.8,450.2,350.1,450.2z"/>
            <path class="st1" d="M512,0C229.2,0,0,229.2,0,512s229.2,512,512,512s512-229.2,512-512S794.8,0,512,0z M748.1,760.6
              c0,34.3-27.3,46.6-61,46.6H321c-33.7,0-61-27.8-61-62.2V279c0-34.4,27.3-62.2,61-62.2h305l122,123.8v110.9L542.5,615.1l-71.2,145.4
              l150.1-40.5l126.6-97.7V760.6z M706.4,532.4l34.9,53.5L600.2,696.9c0,0-57.1,20.1-85.9,28.6c-0.1-0.3-0.3-0.6-0.6-0.9
              c1.1-1.6,50.4-76.7,50.4-76.7L706.4,532.4z M779.8,540.9c-0.6,6.7-3.2,12.4-8,16.5c-6.6,5.7-13.6,10.8-20.7,16.4
              c-11.4-17.4-22.6-34.6-34.1-52.3c7.7-5.8,14.9-11.9,22.7-16.7c7.9-4.9,16.3-3.5,23.6,1.3C775,513.8,781.1,525.3,779.8,540.9z"/>
            <path class="st1" d="M427,587.4h-76.2c-12.6,0-22.9,9.2-22.9,20.6c0,11.4,10.3,20.6,22.9,20.6H427c12.6,0,22.9-9.2,22.9-20.6
              S439.6,587.4,427,587.4z"/>
          </g>`,
  },
})
// 抽奖
Icon.register({
  wall_lottery: {
    width: 1024,
    height: 1024,
    raw: `<g>
    <path  d="M627.924,326.535c0-20.096-14.671-36.417-32.826-36.417c-18.154,0-32.915,16.277-32.915,36.417v36.446
      h32.915C613.253,362.981,627.924,346.629,627.924,326.535z"/>
    <path  d="M428.159,290.058c-18.188,0-32.946,16.292-32.946,36.478c0,20.169,14.715,36.506,32.946,36.506h32.944
      h0.043v-36.506C461.146,306.395,446.39,290.058,428.159,290.058z"/>
    <path d="M512.008,0C229.22,0,0,229.26,0,512c0,282.738,229.221,512,512.009,512C794.797,1024,1024,794.771,1024,512
      C1024,229.232,794.781,0,512.008,0z M788.619,529.622c0,10.371-7.575,18.727-16.954,18.727h-29.668v221.176
      c0,10.341-7.604,18.696-16.923,18.696H298.138c-9.303,0-16.938-8.297-16.938-18.696V548.319h-29.772
      c-9.318,0-16.849-8.326-16.849-18.729v-91.975c0-10.31,7.5-18.742,16.849-18.742H428.16c-46.067,0-83.443-41.376-83.443-92.355
      c0-50.966,37.376-92.342,83.443-92.342c46.063,0,83.443,41.376,83.443,92.342c0-50.966,37.326-92.342,83.495-92.342
      c46.052,0,83.443,41.376,83.443,92.342c0,50.979-37.393,92.355-83.443,92.355h176.628c9.318,0,16.953,8.433,16.895,18.742v92.006
      H788.619z"/>
    <path  d="M465.207,695.519c0,10.28,7.575,18.728,16.847,18.728h66.242c9.318,0,16.925-8.386,16.925-18.728V430.029
      H465.207V695.519z"/>
  </g>`,
  },
})
// 摇号抽奖
Icon.register({
  wall_lotlottery: {
    width: 1024,
    height: 1024,
    raw: `<path d="M512.1,0C229.3,0,0.1,229.2,0.1,512s229.2,512,512,512c282.8,0,512-229.2,512-512C1024.1,229.2,794.9,0,512.1,0  z M771.3,752.7l-1.9,3.2c-7,10.2-16,16-26.9,16H265.7c-12.8,0-21.1-3.2-26.9-9.6c-1.9-1.9-3.2-4.5-3.8-5.8l-1.3-2.6v-2.6  c0.6-147.3,120-266.6,267.1-267.5l22.4-65.2c-28-19.4-46.3-51.7-46.3-88.5c0-59.5,48-107.5,107.5-107.5c59.5,0,107.5,48,107.5,107.5  c0,59.5-48,107.5-107.5,107.5c-0.9,0-1.8,0-2.7,0L563.4,491C681,518.5,769.6,623.8,771.3,748.9V752.7z"/>`,
  },
})
// 弹幕抽奖
Icon.register({
  wall_danmulottery: {
    width: 1024,
    height: 1024,
    raw: `<g>
    <path d="M332.8,606c-14.6,0-26.4,11.9-26.4,26.4c0,14.6,11.8,26.4,26.4,26.4c14.6,0,26.4-11.8,26.4-26.4   C359.2,617.8,347.4,606,332.8,606z"/>
    <polygon points="332.7,472.2 332.7,472.2 332.7,472.2  "/>
    <path d="M332.8,404.6c14.6,0,26.4-11.8,26.4-26.4c0-14.6-11.8-26.4-26.4-26.4c-14.6,0-26.4,11.8-26.4,26.4   C306.3,392.8,318.2,404.6,332.8,404.6z"/>
    <path d="M332.7,472.2c-14.4,0.6-25.9,12.2-25.9,26.4c0,14.3,11.5,25.9,25.9,26.4c14.9,0,27-11.8,27-26.4   C359.6,484,347.6,472.2,332.7,472.2z"/>
    <path d="M512,0C229.2,0,0,229.3,0,512c0,282.7,229.2,512,512,512s512-229.2,512-512C1024,229.2,794.8,0,512,0z    M792,672.6c0,44.3-30.1,80.3-67.2,80.3H299.2c-37.1,0-67.2-35.9-67.2-80.3V351.4c0-44.3,30.1-80.3,67.2-80.3h425.6   c37.1,0,67.2,36,67.2,80.3V672.6z"/>
    <path d="M556.8,605.6H411.2c-12.4,0-22.4,12-22.4,26.8c0,14.8,10,26.8,22.4,26.8h145.6c12.4,0,22.4-12,22.4-26.8   C579.2,617.6,569.1,605.6,556.8,605.6z"/>
    <path d="M713.6,351.4H411.2c-12.4,0-22.4,12-22.4,26.8c0,14.8,10,26.8,22.4,26.8h302.4c12.4,0,22.4-12,22.4-26.8   C736,363.4,726,351.4,713.6,351.4z"/>
    <path d="M646.4,471.8H411.2c-12,0.6-21.5,12.3-21.5,26.8c0,14.4,9.6,26.2,21.5,26.8h0h235.2c12.4,0,22.4-12,22.4-26.8   C668.8,483.8,658.8,471.8,646.4,471.8z"/>
  </g>`,
  },
})
// 图片抽奖
Icon.register({
  wall_piclottery: {
    width: 1024,
    height: 1024,
    raw: `<g>
    <path class="st0" d="M608.8,463.1c26,0,48.8-22.8,48.8-48.8c0-26-22.8-48.8-48.8-48.8c-26,0-48.8,22.8-48.8,48.8   C560,440.3,582.8,463.1,608.8,463.1z"/>
    <path class="st0" d="M690,671.1l-71.5-100.8c-3.3-6.5-13-6.5-19.5-3.3l-3.3,3.3l-6.5,6.5l-45.5,71.5c-6.5,6.5-16.3,9.8-22.8,3.2   c-3.3,0-3.3-3.2-3.3-6.5l-39-55.3l-32.5-52c0-3.3-3.3-3.3-6.5-6.5c-9.8-3.3-19.5-3.3-22.8,6.5l-84.5,130v6.5   c0,9.8,6.5,16.3,16.3,16.3h331.6c6.5,0,13-6.5,13-13C693.3,674.4,693.3,671.1,690,671.1z"/>
    <path class="st0" d="M511.5,0C228.7,0-0.5,229.3-0.5,512s229.2,512,512,512s512-229.2,512-512S794.3,0,511.5,0z M755,690.6   c0,35.8-29.3,65-65,65H332.5c-35.8,0-65-29.3-65-65V333.1c0-35.8,29.3-65,65-65H690c35.8,0,65,29.3,65,65V690.6z"/>
    </g>`,
  },
})
// 红包雨
Icon.register({
  wall_redpack: {
    width: 1024,
    height: 1024,
    raw: `<path  d="M512,0C229.23,0,0,229.23,0,512c0,282.771,229.23,512,512,512c282.771,0,512-229.229,512-512
    C1024,229.23,794.771,0,512,0z M742.963,727.05c0,55.68-10.31,59.802-63.924,59.802H344.968c-51.553,0-63.926-4.122-63.926-59.802
    V379.437c37.118,16.498,78.364,37.119,123.73,51.553c4.12,55.679,49.49,98.981,107.231,98.981s105.171-43.305,107.232-98.981
    c45.367-14.434,86.609-35.055,123.727-53.616V727.05z M559.433,416.556v18.56h-32.997v16.498h32.997v18.56h-32.997v26.806h-30.93
    v-26.806h-32.995v-18.56h32.995v-16.498h-32.995v-18.56h28.868l-37.119-63.927h32.996l26.807,59.802
    c0-2.063,2.063-6.186,4.125-10.312l20.622-49.49h30.934l-37.12,63.927H559.433z M615.11,391.81
    c-14.436-43.306-55.678-74.237-103.108-74.237c-49.491,0-88.672,30.933-103.111,74.237c-49.49-16.499-92.796-39.181-127.853-57.74
    v-26.807c0-55.679,12.374-70.114,63.927-70.114h334.07c51.553,0,63.925,14.437,63.925,70.114v24.743h0.003
    C707.907,350.567,664.602,375.311,615.11,391.81z"/>`,
  },
})
// 套红包
Icon.register({
  wall_ropepack: {
    width: 1024,
    height: 1024,
    raw: `<rect x="251.376" y="232.349" fill="none" width="521.248" height="559.303"/>
    <path  d="M200.777,563.159C200.578,564.685,200.411,566.374,200.777,563.159L200.777,563.159z"/>
    <path  d="M512,0C229.23,0,0,229.229,0,511.999S229.23,1024,512,1024c282.771,0,512-229.229,512-512.001
      C1024,229.229,794.771,0,512,0z M286.473,657.329c-2.756,10.019-13.555,16.271-23.616,13.411
      c-35.047-9.978-69.31-26.452-91.881-55.933c-10.801-14.105-16.852-31.257-18.162-48.913c-1.347-18.126,3.428-35.891,10.826-52.273
      c8.004-17.703,20.064-33.613,33.283-47.738c15.947-17.084,34.274-31.831,53.389-45.215c8.586-6.013,21.305-1.583,26.267,6.889
      c5.616,9.587,1.667,20.277-6.887,26.267c-3.502,2.453-6.982,4.942-10.401,7.513c-0.841,0.632-1.678,1.269-2.514,1.908
      c-0.076,0.058-0.134,0.105-0.22,0.171c-1.431,1.128-2.858,2.262-4.271,3.415c-6.103,4.984-12.065,10.13-17.755,15.562
      c-5.305,5.066-10.446,10.316-15.258,15.854c-1.983,2.28-3.921,4.609-5.804,6.985c-0.466,0.614-0.998,1.313-1.244,1.651
      c-0.866,1.188-1.731,2.383-2.566,3.595c-4.885,7.082-9.318,14.518-12.807,22.393c-0.007,0.016-0.011,0.025-0.017,0.042
      c-0.349,0.894-0.699,1.788-1.026,2.693c-0.64,1.781-1.254,3.567-1.792,5.38c-1.043,3.518-1.943,7.082-2.548,10.699
      c-0.023,0.146-0.05,0.318-0.077,0.506c-0.051,0.488-0.109,0.976-0.148,1.466c-0.137,1.755-0.246,3.511-0.261,5.271
      c-0.016,1.74-0.001,3.488,0.107,5.231c0.056,0.898,0.313,3.021,0.409,3.935c0.509,2.759,1.16,5.491,2.012,8.162
      c0.454,1.423,0.944,2.837,1.48,4.233c0.298,0.642,0.593,1.286,0.914,1.917c1.771,3.491,3.758,6.877,6.036,10.063
      c0.259,0.364,0.527,0.719,0.791,1.081c0.048,0.061,0.097,0.116,0.139,0.169c1.208,1.446,2.442,2.875,3.738,4.241
      c2.903,3.067,5.968,5.985,9.229,8.677c0.524,0.433,1.057,0.859,1.587,1.288c0.039,0.028,0.082,0.061,0.117,0.086
      c1.684,1.229,3.383,2.437,5.125,3.586c7.767,5.118,15.991,9.524,24.512,13.244c0.197,0.086,0.395,0.169,0.592,0.255
      c0.103,0.043,0.187,0.077,0.308,0.127c1.123,0.453,2.246,0.903,3.378,1.338c2.305,0.882,4.617,1.742,6.952,2.547
      c4.843,1.677,9.729,3.201,14.657,4.604C283.122,636.574,289.265,647.177,286.473,657.329z M301.242,308.387
      c0-51.299,11.399-64.599,58.896-64.599h307.785c47.498,0,58.899,13.301,58.899,64.599v22.796h0.002
      c-32.298,17.103-72.197,39.899-117.792,55.099c-13.303-39.896-51.3-68.396-94.997-68.396c-45.598,0-81.698,28.5-95,68.396
      c-45.596-15.199-85.495-36.098-117.792-53.197V308.387z M557.732,409.081L557.732,409.081v17.101h-30.4v15.2h30.4v17.1h-30.4v24.695
      h-28.497v-24.695h-30.398v-17.1h30.4v-15.2h-30.4v-17.1h26.596l-34.198-58.898h30.401l24.697,55.098c0-1.9,1.899-5.699,3.799-9.501
      l19-45.598h28.502l-34.202,58.898H557.732z M726.825,725.115l-0.001,0.206c0,51.297-9.499,54.891-58.897,54.891H360.142
      c-47.496,0-58.896-3.799-58.896-55.097V375.09c34.198,15.199,72.199,34.199,113.995,47.496
      c3.796,51.296,45.598,91.189,98.796,91.189c53.199,0,96.894-39.894,98.795-91.189c41.798-13.297,79.795-32.297,113.993-49.398
      V725.115z M860.284,489.756c-8.1,17.593-20.309,33.502-33.693,47.467c-16.169,16.876-34.726,31.438-54.058,44.507
      c-8.682,5.871-21.239,1.692-26.266-6.887c-5.553-9.476-1.765-20.415,6.887-26.268c3.549-2.397,7.073-4.836,10.539-7.359
      c1.508-1.097,3.011-2.194,4.498-3.315c0.477-0.358,2.367-1.822,3.02-2.319c5.056-4.008,10.021-8.137,14.797-12.472
      c5.221-4.73,10.298-9.629,15.088-14.795c2.144-2.307,4.256-4.645,6.287-7.049c0.997-1.169,1.983-2.341,2.946-3.531
      c0.345-0.425,1.164-1.484,1.687-2.158c4.853-6.53,9.309-13.378,12.963-20.646c0.713-1.42,1.398-2.854,2.063-4.305
      c0.421-1.053,1.178-2.92,1.434-3.645c1.244-3.513,2.347-7.083,3.159-10.719c0.381-1.699,0.718-3.411,1.002-5.13
      c0.07-0.728,0.145-1.453,0.193-2.183c0.231-3.503,0.279-7.023,0.012-10.527c-0.05-0.629-0.104-1.259-0.162-1.886
      c-0.306-1.729-0.646-3.455-1.083-5.155c-0.445-1.745-0.927-3.481-1.505-5.189c-0.304-0.899-1.264-3.244-1.534-3.963
      c-1.278-2.668-2.699-5.267-4.294-7.758c-0.947-1.482-1.925-2.941-2.962-4.36c-0.414-0.509-0.825-1.02-1.256-1.517
      c-2.756-3.202-5.69-6.256-8.832-9.084c-1.506-1.353-3.034-2.686-4.607-3.958c-0.146-0.119-0.341-0.272-0.546-0.433
      c-0.448-0.333-0.892-0.669-1.348-0.996c-7.563-5.415-15.628-10.115-24.029-14.116c-2.208-1.054-4.434-2.072-6.679-3.048
      c-0.351-0.146-0.679-0.281-0.888-0.364c-1.155-0.46-2.31-0.923-3.473-1.364c-4.8-1.827-9.649-3.503-14.546-5.053
      c-9.969-3.156-16.263-13.237-13.406-23.616c2.693-9.797,13.636-16.565,23.614-13.409c31.315,9.91,64.272,25.352,84.864,51.965
      c11.98,15.485,19.479,32.771,21.002,52.434C872.568,455.673,867.787,473.493,860.284,489.756z"/>
    <path  d="M823.573,451.174C823.844,453.259,823.732,452.278,823.573,451.174L823.573,451.174z"/>
    <path d="M225.205,615.975C223.852,614.919,224.425,615.374,225.205,615.975L225.205,615.975z"/>`,
  },
})
// 语音红包
Icon.register({
  wall_voicepack: {
    width: 1024,
    height: 1024,
    raw: `<g>
    <path  d="M595.108,648.512c-3.38,0-6.156,1.795-6.51,4.081h-0.249c-2.525,30.288-35.115,53.905-74.473,53.905
      c-39.367,0.001-71.96-23.615-74.482-53.904h-0.228c-0.353-2.41-3.079-4.205-6.219-4.08c-3.142,0.154-5.588,2.162-5.602,4.572
      c0.014,0.434,0.102,0.869,0.267,1.27c3.115,32.823,36.538,59.063,78.994,62.031v26.734h-15.854
      c-2.85-0.032-5.486,1.144-6.912,3.028c-1.437,1.886-1.437,4.236,0,6.149c1.426,1.887,4.063,3.029,6.912,3.029h47.545
      c2.875,0,5.501-1.143,6.912-3.029c1.462-1.913,1.462-4.264,0-6.149c-1.411-1.885-4.037-3.061-6.912-3.028h-15.843v-26.734
      c42.479-2.969,75.932-29.239,79.01-62.124c0.152-0.372,0.252-0.772,0.252-1.178C601.718,650.553,598.767,648.512,595.108,648.512z"
      />
    <path  d="M513.89,684.117h1.291c15.223,0.03,29.83-6.38,40.625-17.794c10.772-11.418,16.854-26.919,16.854-43.067
      v-71.689c0-16.161-6.081-31.649-16.854-43.068c-10.794-11.416-25.402-17.825-40.625-17.797h-1.291
      c-31.712-0.041-57.442,27.214-57.48,60.865v71.689C456.447,656.919,482.177,684.176,513.89,684.117z"/>
    <path  d="M513.5,1.8c-282.77,0-512,229.23-512,512c0,282.771,229.23,512,512,512c282.771,0,512-229.229,512-512
      C1025.5,231.03,796.271,1.8,513.5,1.8z M685.193,790.935H341.806c-32.729,0-59.255-23.43-59.255-52.294V413.235
      c64.996,24.601,144.925,39.116,231.39,39.116c86.061,0,165.676-14.388,230.507-38.791l0.001,325.081
      C744.448,767.506,717.911,790.935,685.193,790.935z M744.448,386.999c-62.462,24.006-142.83,38.472-230.508,38.472
      c-88.092,0-168.789-14.601-231.389-38.814V282.917c0-25.54,26.525-46.251,59.256-46.251h343.386
      c32.718,0,59.255,20.712,59.255,46.251V386.999z"/>
  </g>`,
  },
})
// 摇一摇
Icon.register({
  wall_shake: {
    width: 1024,
    height: 1024,
    raw: `<g>
    <rect x="401.718" y="318.509"  width="208.49" height="386.979"/>
    <path  d="M512,0C229.23,0,0,229.23,0,512c0,282.771,229.23,512,512,512c282.771,0,512-229.229,512-512
      C1024,229.23,794.771,0,512,0z M325.376,581.096l-37.29,45.016l28.255,39.197l-26.928,32.492l-53.267-73.938l37.273-45.047
      l-40.148-55.733l40.965-49.435l-38.891-53.98l55.834-67.47l25.042,34.755l-28.921,34.927l38.875,53.979l-40.934,49.471
      L325.376,581.096z M652.957,730.775c0,26.211-22.387,47.461-50,47.461H408.969c-27.613,0-50-21.25-50-47.461V293.223
      c0-26.212,22.387-47.46,50-47.46h193.988c27.613,0,50,21.248,50,47.46V730.775z M786.715,628.389l-50.56,69.41l-26.608-33.275
      l25.109-34.467l-36.813-46.07l40.829-56.105l-40.489-50.627l39.57-54.32l-28.547-35.761l25.45-34.975l55.188,69.052l-39.569,54.285
      l40.454,50.646l-40.829,56.07L786.715,628.389z"/>
  </g>`,
  },
})
// 敲敲乐
Icon.register({
  wall_diglett: {
    width: 1024,
    height: 1024,
    raw: `<g>
    <path class="st0" d="M512,0C229.2,0,0,229.2,0,512c0,282.8,229.2,512,512,512c282.8,0,512-229.2,512-512C1024,229.2,794.8,0,512,0z   M514.8,172L514.8,172c7.1-5.3,17.2-3.8,22.5,3.3l117.8,158c5.3,7.1,3.8,17.2-3.3,22.5c-7.1,5.3-17.2,3.8-22.5-3.3l-117.8-158  C506.2,187.4,507.7,177.3,514.8,172z M230.7,383.9L230.7,383.9c7.1-5.3,17.2-3.9,22.5,3.3l117.9,158.1c5.3,7.1,3.8,17.2-3.3,22.5  l0,0c-7.1,5.3-17.2,3.8-22.5-3.3L227.4,406.4C222.1,399.3,223.6,389.2,230.7,383.9z M552.3,831.7c0,8.9-7.2,16.1-16.1,16.1H263.1  c-8.9,0-16.1-7.2-16.1-16.1v-32.2c0-8.9,7.2-16.1,16.1-16.1h273.1c8.9,0,16.1,7.2,16.1,16.1V831.7z M702.5,787L499,477.4  c-2.3-3.5-3.1-7.7-2.4-11.6l-82.5,61.6c-7.1,5.3-17.2,3.9-22.5-3.3L279.3,373.7c-5.3-7.1-3.9-17.2,3.3-22.5L475.7,207  c7.1-5.3,17.2-3.9,22.5,3.3l112.3,150.5c5.3,7.1,3.9,17.2-3.3,22.5l-82.5,61.6c3.9,0.5,7.7,2.4,10.4,5.6l238.8,283.4  c6,7.1,4.7,17.7-2.7,23.3l-45.6,34C718.2,796.6,707.6,794.8,702.5,787z M805,805.4l-42.8,31.9c-7.1,5.3-17.2,3.8-22.5-3.3  c-5.3-7.1-3.8-17.2,3.3-22.5l42.8-31.9c7.1-5.3,17.2-3.8,22.5,3.3l0,0C813.6,790,812.1,800,805,805.4z"/>
  </g>`,
  },
})
// 数钱
Icon.register({
  wall_money: {
    width: 1024,
    height: 1024,
    raw: `<path  d="M512,0C229.23,0,0,229.23,0,512c0,282.771,229.23,512,512,512c282.771,0,512-229.229,512-512
    C1024,229.23,794.771,0,512,0z M365.622,721.134L233.055,312.54c-3.66-12.241-1.67-25.317,5.528-36.205
    c7.335-10.903,19.12-18.757,32.789-21.808l99.856-23.268c-3.617,6.47-5.529,13.608-5.606,20.868V721.134L365.622,721.134z
     M651.153,503.016h-24.177c-0.485-14.229-4.802-28.096-12.452-40.32c-9.6-14.048-26.334-22.219-43.922-21.445
    c-19.742-0.046-37.724,10.767-46.139,27.731c-0.744,1.17-5.226,12.59-13.455,34.034h-60.416c0.366-5.756,2.355-11.313,5.757-16.083
    c5.982-6.97,14.746-11.269,24.18-11.891V444.76c-16.738,0.94-32.365,8.246-43.39,20.258c-8.415,11.088-13.107,24.329-13.458,37.998
    h-22.46v17.466h22.46c0.486,13.518,4.848,26.669,12.623,37.968c9.11,13.243,24.86,20.959,41.477,20.289
    c19.804,0.305,37.92-10.57,46.122-27.73l11.525-30.525h64.243c-0.639,7.017-3.494,13.668-8.232,19.104
    c-8.564,6.984-19.5,10.782-30.77,10.72v30.771c17.586,0.94,34.778-5.043,47.597-16.554c12.575-11.424,19.38-27.488,18.587-44.043
    h24.301v223.918c0,12.452-4.98,24.299-13.728,32.926c-8.78,8.868-20.655,13.818-32.985,13.79H417.942
    c-12.347,0.028-24.208-4.922-32.97-13.79c-8.764-8.627-13.731-20.474-13.744-32.926v-466.44
    c0.271-26.015,21.138-46.898,46.638-46.701H604.44c12.331-0.03,24.206,4.922,32.985,13.776c8.746,8.625,13.728,20.501,13.728,32.925
    V503.016z M581.172,511.188h-69.979c-2.646,19.68-16.268,35.536-34.234,39.881c-11.465,1.004-22.493-4.952-28.627-15.462
    c-4.16-7.409-6.605-15.778-7.122-24.419h69.983c0.166-1.384,0.605-2.734,1.274-3.935h0.03c2.368-18.575,16.553-33.274,34.718-36.039
    c10.508-0.273,20.319,5.284,25.636,14.534C577.438,493.495,580.262,502.181,581.172,511.188z M789.6,336.323L686.328,751.143
    V282.363c-0.062-7.366-1.824-14.61-5.164-21.11l75.936,17.603c11.663,3.083,21.687,10.888,27.792,21.687
    S792.7,324.219,789.6,336.323z"/>`,
  },
})
// 拔河
Icon.register({
  wall_tugwar: {
    width: 1024,
    height: 1024,
    raw: `<path  d="M512.008,0C229.22,0,0,229.26,0,512c0,282.738,229.221,512,512.009,512C794.797,1024,1024,794.771,1024,512
    C1024,229.232,794.781,0,512.008,0z M764.563,512h-95.323c2.114,7.148,3.274,14.552,3.445,22.005
    c-1.943,25.282-9.859,49.745-23.132,71.272c-8.973,15.011-20.744,46.467-45.955,46.467l-14.876,25.52
    c-0.206,0.546-0.546,1.024-1.024,1.364l32.821,81.063c1.639,6.005,0.888,12.384-2.184,17.774c-3.071,5.393-8.12,9.314-14.056,10.883
    c-12.282,3.174-24.838-4.095-28.319-16.442l-35.583-88.365c-2.114-8.051,0.238-16.614,6.21-22.381c0.341-2.56,1.159-5.049,2.423-7.3
    l1.364-2.116h-39.012c-7.079,0-14.058-1.67-20.387-4.913l-17.279,30.433c-1.929,3.411-4.812,6.211-8.29,8.021v82.802
    c0,12.931-10.354,23.403-23.15,23.403c-12.775,0-23.131-10.474-23.131-23.403v-92.937c-0.086-9.759,6.039-18.491,15.198-21.663
    l50.084-87.683c2.287-9.998,3.565-20.164,3.804-30.397H373.17c-16.428-0.035-31.593-8.974-39.729-23.406h-75.313
    c-12.691,0-22.962-10.404-22.962-23.232c0-12.827,10.271-23.233,22.962-23.233h75.313c8.136-14.432,23.301-23.354,39.729-23.403
    h98.462c8.136-14.433,23.284-23.371,39.73-23.405H557.3c-50.801-0.103-91.944-41.812-91.894-93.191
    c0.05-51.396,41.263-93.021,92.063-93.021c50.833,0,92.047,41.624,92.082,93.021c0.034,51.38-41.111,93.089-91.879,93.191h45.924
    c25.211,0,31.797,20.607,45.955,46.809h115.009c12.69,0,22.996,10.405,22.996,23.233S777.252,512,764.563,512z"/>`,
  },
})
// 最佳射手
Icon.register({
  wall_shoot: {
    width: 1024,
    height: 1024,
    raw: `<path d="M512.008,0C229.22,0,0,229.26,0,512c0,282.738,229.221,512,512.009,512C794.797,1024,1024,794.771,1024,512
    C1024,229.232,794.781,0,512.008,0z M511.495,236.435c47.785,0,92.72,12.144,131.868,33.583l-48.397,83.635v-0.016H429.859
    l-48.875-84.41C419.833,248.281,464.276,236.435,511.495,236.435z M677.556,510.913l-82.591,142.802H429.873l-82.561-142.802
    l82.561-142.819h165.092L677.556,510.913z M366.289,278.044l49.262,85.276l-82.516,142.759h-96.6
    C238.314,409.703,289.696,325.649,366.289,278.044z M236.465,517.104h99.121l82.516,142.848l-49.783,86.111
    C290.71,698.71,238.449,614.181,236.465,517.104z M511.479,786.403c-47.383,0-92.019-11.875-130.912-33.001l49.306-85.275h165.092
    l48.814,84.441C604.513,774.17,559.457,786.403,511.479,786.403z M656.133,745.139l-47.801-82.65l82.591-142.863h95.48
    C783.539,614.956,732.248,698.024,656.133,745.139z M690.894,504.138l-82.562-142.757l48.218-83.383
    c76.146,47.323,127.286,130.571,129.854,226.14H690.894z"/>`,
  },
})
// 答题
Icon.register({
  wall_answerrace: {
    width: 1024,
    height: 1024,
    raw: `<g>
    <path  d="M676.851,594.509H380.357c-9.101,0-16.468,7.415-16.468,16.471c0,9.115,7.368,16.499,16.468,16.499h296.493
      c9.085,0,16.499-7.384,16.499-16.499C693.35,601.924,685.936,594.509,676.851,594.509z"/>
    <path  d="M377.396,429.534l2.962,0.273h164.717c9.112,0,16.468-7.385,16.468-16.468
      c0-9.1-7.354-16.483-16.468-16.483H380.357c-9.101,0-16.468,7.384-16.468,16.483C363.89,421.42,369.708,428.153,377.396,429.534z"
      />
    <path  d="M676.851,495.698H380.357c-9.101,0-16.468,7.367-16.468,16.468c0,8.083,5.818,14.814,13.506,16.229
      l2.962,0.241h296.493c9.085,0,16.499-7.369,16.499-16.47C693.35,503.065,685.936,495.698,676.851,495.698z"/>
    <path  d="M512,0C229.23,0,0,229.23,0,512c0,282.771,229.23,512,512,512c282.771,0,512-229.229,512-512
      C1024,229.23,794.771,0,512,0z M273.055,312.934v347.48H260.43c-7.476,0.091-14.691-3.069-20.008-8.72
      c-5.317-5.685-8.312-13.37-8.312-21.453v-367.67c0-16.803,12.687-30.43,28.319-30.43h437.781c7.475,0,14.646,3.221,19.933,8.933
      c5.288,5.712,8.203,13.46,8.146,21.497v16.332H304.472C287.138,279.192,273.19,294.308,273.055,312.934z M791.889,697.879
      L791.889,697.879c-0.123,15.617-10.97,28.501-25.007,30.354l-3.282,0.243H616.173l-48.859,52.839
      c-0.577,0.606-1.185,1.216-1.82,1.793c-12.397,11.455-31.236,11.577-43.771,0.882l-2.765-2.675l-48.873-52.839H325.771
      c-14.417-0.151-26.235-11.941-27.863-27.043l-0.183-3.556v-369.54c0-15.542,10.833-28.349,24.779-30.112l3.267-0.198h437.313
      c7.563-0.136,14.855,2.978,20.266,8.66c4.497,4.756,7.385,10.954,8.266,17.608l0.273,4.042V697.879z"/>
    </g>`,
  },
})
// 你演我猜
Icon.register({
  wall_playguess: {
    width: 1024,
    height: 1024,
    raw: `<g>
    <path  d="M512,0C229.23,0,0,229.23,0,512c0,282.771,229.23,512,512,512c282.771,0,512-229.229,512-512
      C1024,229.23,794.771,0,512,0z M379.875,711.047c-4.145,23.295-11.156,41.166-21.018,53.601
      c-9.871,12.434-22.795,19.834-38.78,22.202c-15.985,2.367-35.228,0.193-57.726-6.514l-5.328-73.414l39.667-1.184l-1.776-113.677
      l-56.246,17.17l-7.104-70.454l62.167-17.171l-1.776-117.211l-53.286,21.906l-7.104-67.495l42.628-18.354l-40.852-69.863
      l86.44-30.787l33.748,64.599l51.509-22.499l8.289,71.732l-34.339,14.228l5.921,254.884
      C385.694,658.316,384.02,687.754,379.875,711.047z M756.72,779.744l-134.396,2.961l-2.96-51.51l47.957-0.593l0.591-25.459
      l-130.845-1.184l0.592,76.375l-87.031-2.367l-2.96-258.729l311.422-5.905L756.72,779.744z M788.69,494.389l-378.917,0.593v-52.693
      l151.565-1.776v-27.235l-106.569,1.184l-0.592-56.246h106.569v-26.643l-122.555-0.601v-51.5l121.963-2.332v-35.56h88.809
      l-1.184,33.747l127.292-2.96v59.205H646.597l-0.592,26.643l111.308-0.592V411.5l-112.491,1.185l-0.593,26.642l146.238-2.368
      L788.69,494.389z"/>
    <polygon  points="670.278,564.828 535.882,564.236 535.882,584.957 669.688,585.549 	"/>
    <polygon points="536.474,653.043 668.503,653.043 669.095,633.506 536.474,632.322 	"/>
  </g>`,
  },
})
// 答题红包
Icon.register({
  wall_answer: {
    width: 1024,
    height: 1024,
    raw: `<g>
    <path  d="M511.101,302.478c10.497,0,19.005-8.502,19.005-19c0-10.497-8.508-19-19.005-19
      c-10.496,0-18.995,8.503-18.995,19C492.106,293.976,500.604,302.478,511.101,302.478z"/>
    <path  d="M359.155,582.039h72.791c4.012,0,7.275-2.824,7.275-6.365v-6.363c0-3.514-3.264-6.361-7.275-6.361h-72.791
      c-4.013,0-7.277,2.848-7.277,6.361v6.363C351.878,579.215,355.143,582.039,359.155,582.039z"/>
    <path  d="M359.155,435.571h298.412c4.012,0,7.275-2.851,7.275-6.364v-6.364c0-3.525-3.264-6.376-7.275-6.376H359.155
      c-4.013,0-7.277,2.851-7.277,6.376v6.364C351.878,432.721,355.143,435.571,359.155,435.571z"/>
    <path  d="M657.567,639.365H359.155c-4.013,0-7.277,2.848-7.277,6.361v6.363c0,3.541,3.265,6.391,7.277,6.391h298.412
      c4.012,0,7.275-2.85,7.275-6.391v-6.363C664.844,642.242,661.58,639.365,657.567,639.365z"/>
    <path  d="M359.155,512.001H512c4.024,0,7.277-2.85,7.277-6.377v-6.365c0-3.511-3.253-6.361-7.277-6.361H359.155
      c-4.013,0-7.277,2.851-7.277,6.361v6.365C351.878,509.151,355.143,512.001,359.155,512.001z"/>
    <path  d="M512,0C229.23,0,0,229.229,0,512c0,282.77,229.23,512,512,512c282.771,0,512-229.23,512-512
      C1024,229.229,794.771,0,512,0z M766.742,734.898c0,17.568-16.296,31.844-36.381,31.844H293.654
      c-20.1,0-36.396-14.275-36.396-31.844v-413.95c0-17.597,16.296-31.846,36.396-31.846h175.295
      c3.46-18.08,21.416-31.845,43.051-31.845c21.649,0,39.579,13.765,43.063,31.845h175.298c20.086,0,36.381,14.249,36.381,31.846
      V734.898z"/>
    <path fill="#FBBA1E" d="M662.909,498.845l-88.894,83.251l-35.717-34.197c-3.819-3.65-10.016-3.65-13.847,0
      c-3.818,3.648-3.818,9.6,0,13.25l42.677,40.813c1.823,1.766,4.315,2.736,6.887,2.736c2.599,0,5.063-0.969,6.918-2.736l95.81-89.877
      c3.818-3.653,3.818-9.585,0-13.239C672.924,495.221,666.727,495.221,662.909,498.845z"/>
  </g>`,
  },
})
// 投票
Icon.register({
  wall_vote: {
    width: 1024,
    height: 1024,
    raw: `<circle  cx="512" cy="512" r="512"/>
    <g>
      <path fill="#FFFFFF" d="M364.886,449.968h-88.77c-8.449,0-17.751,6.743-17.751,17.725v280.216c0,8.422,6.771,16.899,17.751,16.899
        h88.77c8.463,0,17.766-6.771,17.766-17.749V466.837C381.799,456.709,375.041,449.968,364.886,449.968z"/>
      <path fill="#FFFFFF" d="M552.566,258.366h-80.309c-11.834,0-21.137,8.434-21.137,21.108v465.081
        c0,11.774,8.463,21.077,21.137,21.077h80.309c11.834,0,21.135-8.422,21.135-21.077V279.475
        C573.701,266.801,565.279,258.366,552.566,258.366z"/>
      <path fill="#FFFFFF" d="M746.205,364.721h-83.719c-10.129,0-19.432,8.437-19.432,19.402v362.082c0,10.127,8.451,19.43,19.432,19.43
        h83.719c10.127,0,19.43-8.421,19.43-19.43V383.283C765.635,373.157,757.188,364.721,746.205,364.721z"/>
    </g>`,
  },
})
// 打赏
Icon.register({
  wall_reward: {
    width: 1024,
    height: 1024,
    raw: `<g>
    <path  d="M512,0C229.23,0,0,229.23,0,512c0,282.771,229.23,512,512,512c282.771,0,512-229.229,512-512
      C1024,229.23,794.771,0,512,0z M429.841,247.318c30.519,0,82.676-14.705,82.676-14.705s48.35,14.705,78.884,14.705
      c30.53,0,82.735-15.145,82.735-15.145l-68.593,71.295H416.47l-66.833-70.4C349.638,233.068,399.292,247.318,429.841,247.318z
       M562.264,790.271c-47.258,2.976-102.738,0-102.738,0s-227.361-4.916-227.361-176.765c0-171.848,179.603-282.601,179.603-282.601
      h196.751c0,0,183.079,132.635,183.079,285.757C791.597,769.844,562.264,791.789,562.264,790.271z"/>
    <path  d="M562.626,424.145l-40.428,78.109c-4.645,9.15-8.225,17.74-10.729,25.842
      c-2.124-7.479-5.69-16.054-10.729-25.842l-39.671-78.109h-61.08l61.706,115.669H409.9v40.488h73.586v22.034H409.9v40.519h73.586
      v50.021h55.771v-50.021H613.8v-40.519h-74.544v-22.034H613.8v-40.488h-52.539l62.493-115.669H562.626z"/>
  </g>`,
  },
})
// 主题墙
Icon.register({
  wall_topic: {
    width: 1024,
    height: 1024,
    raw: `<path d="M512.009,0C229.221,0,0.001,229.26,0.001,512c0,282.738,229.221,512,512.009,512  C794.797,1024,1024,794.771,1024,512C1024,229.232,794.781,0,512.009,0z"/>
      <polygon points="332.67,472.175 332.685,472.176 332.691,472.175 "/>
      <path fill="#FFFFFF" d="M776.121,491.546L521.323,267.818c-6.214-6.215-16.573-6.215-22.786,0L247.879,493.616  c-4.144,4.143-6.213,12.429-4.144,18.645c2.073,6.214,8.286,10.356,16.572,10.356h35.217v176.081  c0,6.216-2.072,29.002,12.429,45.573c6.213,8.287,18.642,16.572,39.36,16.572h319.018c8.286,0,26.929-2.069,41.431-14.498  c8.286-8.287,18.643-22.789,18.643-45.574V522.617l39.36-2.07c6.213,0,12.429-4.143,14.499-10.359  C782.337,503.975,780.265,495.688,776.121,491.546L776.121,491.546z M450.989,653.232c0-33.145,28.903-56.04,62.048-56.04  c33.144,0,60.075,26.931,60.075,60.076V727.7H450.891v-70.432L450.989,653.232z"/>
      `,
  },
})
// 评分
Icon.register({
  wall_mark: {
    width: 1024,
    height: 1024,
    raw: `<path d="M512.008,0C229.22,0,0,229.26,0,512c0,282.738,229.221,512,512.009,512C794.797,1024,1024,794.771,1024,512
    C1024,229.232,794.781,0,512.008,0z M785.627,492.055L681.369,579.85c-7.32,5.484-9.148,14.634-7.32,23.775l23.781,140.844
    c1.83,10.978,0,20.119-9.146,25.607c-5.485,3.66-10.975,5.487-18.291,5.487c-5.485,0-9.146-1.827-14.635-3.655l-131.693-67.679
    c-7.32-3.657-18.308-3.657-25.625,0L372.23,773.736c-10.977,5.485-21.95,3.655-32.927-1.828
    c-9.146-7.32-16.461-14.636-14.633-25.611l31.097-144.5c1.828-9.146-1.828-18.291-7.316-23.777l-109.749-85.964
    c-9.146-7.316-10.974-20.121-7.315-31.097c3.658-10.974,12.804-18.291,23.778-20.119l142.672-25.608
    c9.147-1.829,16.463-7.317,20.121-14.633l65.849-137.185c5.487-9.146,16.463-16.462,27.448-16.462
    c10.979,0,21.95,7.317,27.44,16.462l64.018,137.185c3.66,7.316,12.807,14.633,20.121,14.633l146.332,25.608
    c10.973,1.829,20.121,9.146,23.777,20.119C796.602,471.934,792.943,482.908,785.627,492.055z"/>`,
  },
})
// 全屏
Icon.register({
  wall_fullscreen: {
    width: 34,
    height: 34,
    raw:
      '<circle cx="17" cy="17" r="17"/><path fill="#FFFFFF" d="M10.696,9.169c1.16,1.317,2.605,2.653,4.017,4.064c0.399,0.399,1.124,0.974,1.243,1.435  c0.186,0.717-0.415,1.496-1.243,1.339c-0.557-0.105-1.038-0.799-1.482-1.243c-1.347-1.347-2.842-2.811-4.112-4.064  c-0.106,0.726,0.163,1.449-0.048,2.008c-0.292,0.776-1.676,0.776-1.96,0c-0.215-0.587-0.048-1.61-0.048-2.438  c0-0.895,0-1.77,0-2.487c0.17-0.365,0.463-0.652,1.004-0.717c0.866-0.104,3.205-0.119,4.16,0c0.977,0.122,1.51,1.651,0.431,2.056  C12.11,9.327,11.408,9.067,10.696,9.169L10.696,9.169z M24.85,10.747c-1.316,1.161-2.652,2.605-4.064,4.017  c-0.399,0.399-0.974,1.124-1.435,1.243c-0.739,0.192-1.528-0.459-1.339-1.291c0.114-0.502,0.83-1.069,1.243-1.482  c1.323-1.322,2.84-2.77,4.064-4.064c-0.796-0.102-1.451,0.162-2.008-0.048c-1.079-0.405-0.595-1.927,0.478-2.056  c0.651-0.078,1.538,0,2.486,0c1.113,0,2.123-0.12,2.487,0.431c0.339,0.513,0.19,1.66,0.19,2.63c0,0.943,0.154,2.121-0.143,2.678  c-0.364,0.682-1.637,0.639-1.913-0.096C24.681,12.131,24.96,11.551,24.85,10.747L24.85,10.747z M10.648,24.9  c0.726,0.106,1.448-0.162,2.008,0.048c0.776,0.292,0.775,1.676,0,1.961c-0.587,0.215-1.611,0.048-2.439,0.048  c-0.895,0-1.77,0-2.486,0c-0.299-0.147-0.522-0.37-0.669-0.67c0-0.717,0-1.59,0-2.486c0-0.828-0.167-1.852,0.048-2.438  c0.285-0.776,1.669-0.776,1.96,0c0.205,0.546-0.054,1.248,0.048,1.961c1.226-1.071,2.7-2.652,4.064-4.018  c0.409-0.408,0.967-1.121,1.435-1.243c0.772-0.2,1.495,0.417,1.339,1.243c-0.105,0.558-0.793,1.032-1.243,1.482  C13.356,22.146,11.886,23.646,10.648,24.9L10.648,24.9z M24.85,23.37c0.109-0.808-0.167-1.437,0.048-2.008  c0.279-0.744,1.538-0.798,1.912-0.096c0.315,0.589,0.144,1.739,0.144,2.678c0,1.344,0.219,2.714-0.717,3.013  c-0.718,0-1.591,0-2.486,0c-0.828,0-1.852,0.167-2.438-0.048c-0.773-0.284-0.781-1.675,0-1.961c0.548-0.201,1.246,0.052,1.96-0.048  c-1.068-1.224-2.637-2.686-4.017-4.064c-0.52-0.52-1.517-1.161-1.243-2.104c0.116-0.401,0.751-0.873,1.387-0.669  c0.443,0.143,1.055,0.911,1.435,1.291C22.106,20.627,23.661,22.252,24.85,23.37L24.85,23.37z"/>',
  },
})
// ×
Icon.register({
  common_x: {
    width: 360,
    height: 360,
    raw:
      '<path d="M360,310.076L226.384,179.407L359.381,49.332L313.015,3.97L180,134.038L47.002,3.97L0,49.933L133.015,180L0,310.076l46.383,45.344l132.998-130.05l133.633,130.66L360,310.076L360,310.076z"/>',
  },
})
// qrcode
Icon.register({
  common_qrcode: {
    width: 360,
    height: 360,
    raw:
      '<path d="M0.59-0.21c39.597,0,79.195,0,118.792,0c0,39.935,0,79.869,0,119.803c-39.766,0-79.531,0-119.297,0c0-39.429,0-78.857,0-118.287C0.042,0.589,0.001-0.125,0.589-0.209L0.59-0.21z M16.26,18.494c0,28.308,0,56.616,0,84.923c28.982,0,57.964,0,86.946,0c0-28.982,0-57.964,0-86.945c-28.14,0-56.279,0-84.418,0C17.888,17.09,18.685,19.4,16.26,18.494L16.26,18.494z M241.206-0.21c39.597,0,79.195,0,118.792,0c0,39.935,0,79.869,0,119.803c-39.766,0-79.531,0-119.297,0c0-39.429,0-78.857,0-118.287c-0.043-0.717-0.083-1.431,0.504-1.515L241.206-0.21z M256.877,17.988c0,28.477,0,56.953,0,85.429c28.982,0,57.964,0,86.946,0c0-24.293,0-51.881,0-75.824c0-3.083,1.451-9.279-2.023-9.098c-0.85-3.696-6.209-2.023-8.593-2.023c-25.134,0-51.568,0-75.825,0C256.792,16.555,256.831,17.27,256.877,17.988L256.877,17.988z M34.963,34.669c16.682,0,33.363,0,50.044,0c0,16.85,0,33.7,0,50.549c-16.85,0-33.7,0-50.55,0c0.337-16.681-0.674-34.71,0.504-50.549H34.963zM275.581,34.669c16.681,0,33.363,0,50.044,0c0,16.85,0,33.7,0,50.549c-16.85,0-33.699,0-50.55,0c0.337-16.681-0.675-34.71,0.504-50.549H275.581z M0.59,137.791c5.225,0,10.446,0,15.671,0c0,28.308,0,56.616,0,84.923c-5.392,0-10.784,0-16.175,0C0.422,194.574-0.589,165.087,0.59,137.791z M359.999,137.791c0,5.392,0,10.784,0,16.175c-11.458,0-22.916,0-34.374,0c0-5.392,0-10.783,0-16.175C337.083,137.791,348.541,137.791,359.999,137.791z M359.999,172.165c0,5.392,0,10.784,0,16.175c-5.393,0-10.783,0-16.176,0c0-5.392,0-10.783,0-16.175C349.216,172.165,354.607,172.165,359.999,172.165z M0.59,240.912c39.597,0,79.195,0,118.792,0c0,39.766,0,79.531,0,119.298c-39.766,0-79.531,0-119.297,0c0-39.261,0-78.522,0-117.781C0.042,241.711,0.001,240.997,0.59,240.912L0.59,240.912z M16.26,259.11c0,23.924,0,50.408,0,73.804c0,3.282-1.208,8.291,1.516,9.098c0.664-0.14,0.252,0.738,0.504,1.011c2.151,2.297,15.14,1.012,20.22,1.012c22.15,0,45.588,0,64.703,0c0-28.982,0-57.964,0-86.945c-28.14,0-56.279,0-84.418,0c-0.9,0.618-0.103,2.929-2.527,2.023L16.26,259.11z M343.823,257.088c0,6.066,0,12.133,0,18.199c-11.457,0-22.916,0-34.373,0c0-6.066,0-12.133,0-18.199C320.907,257.088,332.366,257.088,343.823,257.088z M34.963,275.286c16.682,0,33.363,0,50.044,0c0,16.85,0,33.7,0,50.55c-16.85,0-33.7,0-50.55,0c0.337-16.682-0.674-34.71,0.504-50.55H34.963z M222.503,309.659c0,5.393,0,10.784,0,16.176c-5.392,0-10.783,0-16.175,0c0-5.393,0-10.783,0-16.176C211.72,309.659,217.111,309.659,222.503,309.659L222.503,309.659zM309.449,325.836c5.392,0,10.783,0,16.175,0c0,6.066,0,12.132,0,18.198c-5.392,0-10.783,0-16.175,0C309.449,337.968,309.449,331.901,309.449,325.836z M343.823,325.836c5.393,0,10.784,0,16.176,0c0,6.066,0,12.132,0,18.198c-5.393,0-10.783,0-16.176,0C343.823,337.968,343.823,331.901,343.823,325.836z M188.13,206.538c0,5.393,0,10.784,0,16.176c-5.225,0-10.447,0-15.672,0c-1.175,5.058-0.169,12.298-0.504,18.199c-6.065,0-12.132,0-18.198,0c0,5.224,0,10.446,0,15.671c5.057,1.176,12.297,0.168,18.198,0.504c0,6.066,0,12.132,0,18.198c5.392,0,10.784,0,16.176,0c0,5.393,0,10.784,0,16.176c6.066,0,12.132,0,18.198,0c0-5.392,0-10.783,0-16.176c16.682,0,33.363,0,50.044,0c1.179-10.447,0.168-23.084,0.504-34.373c6.066,0,12.133,0,18.199,0c0,5.393,0,10.783,0,16.175c5.225,0,10.446,0,15.671,0c1.178-10.446,0.167-23.084,0.505-34.373c6.066,0,12.131,0,18.197,0c0-5.225,0-10.447,0-15.671c-10.446-1.179-23.084-0.168-34.373-0.505c0,5.393,0,10.784,0,16.176c-6.066,0-12.132,0-18.198,0c0,6.066,0,12.132,0,18.198c-5.393,0-10.784,0-16.176,0c0-6.066,0-12.132,0-18.198c5.392,0,10.783,0,16.176,0c0-5.225,0-10.447,0-15.671c-4.383-1.176-10.951-0.17-16.176-0.505c0-6.066,0-12.132,0-18.198c16.85,0,33.7,0,50.55,0c0-16.85,0-33.7,0-50.55c6.067,0,12.133,0,18.199,0c0,11.458,0,22.916,0,34.374c5.392,0,10.783,0,16.175,0c0,16.849,0,33.699,0,50.55c-5.225,0-10.446,0-15.671,0c-1.178,10.447-0.167,23.084-0.504,34.373c-5.897,0-11.796,0-17.692,0c-1.179,10.448-0.168,23.084-0.505,34.374c17.524,0,35.048,0,52.571,0c0-5.392,0-10.783,0-16.176c5.393,0,10.784,0,16.176,0c0,5.393,0,10.784,0,16.176c-5.225,0-10.446,0-15.671,0c-1.176,5.057-0.169,12.298-0.505,18.198c-22.916,0-45.831,0-68.748,0c0,11.458,0,22.915,0,34.374c5.393,0,10.784,0,16.176,0c0,5.392,0,10.783,0,16.175c-5.392,0-10.783,0-16.176,0c0-5.225,0-10.446,0-15.671c-4.383-1.176-10.951-0.17-16.176-0.504c0.393-1.741-0.281-2.416-2.022-2.023c0-5.393,0-10.784,0-16.176c-5.225,0-10.446,0-15.671,0c-1.178,10.447-0.167,23.085-0.505,34.374c-6.065,0-12.132,0-18.198,0c0-11.458,0-22.916,0-34.374c6.066,0,12.133,0,18.198,0c0-11.289,0-22.578,0-33.867c-10.279-1.179-22.746-0.168-33.867-0.505c-1.176,5.057-0.17,12.298-0.505,18.198c-6.066,0-12.132,0-18.198,0c0,11.458,0,22.915,0,34.374c-5.392,0-10.784,0-16.176,0c0,5.392,0,10.783,0,16.175c-11.458,0-22.916,0-34.374,0c0-5.392,0-10.783,0-16.175c11.458,0,22.916,0,34.374,0c0-5.898,0-11.796,0-17.692c-10.447-1.179-23.083-0.168-34.374-0.505c0-11.457,0-22.915,0-34.373c11.458,0,22.916,0,34.374,0c0-5.225,0-10.447,0-15.672c-10.447-1.177-23.083-0.167-34.374-0.504c0-17.524,0-35.048,0-52.571c5.392,0,10.784,0,16.175,0c0-5.393,0-10.783,0-16.176c5.898,0,11.795,0,17.693,0c1.178-10.447,0.167-23.084,0.504-34.374c-6.066,0-12.132,0-18.199,0c0,11.458,0,22.916,0,34.374c-5.392,0-10.784,0-16.175,0c0-17.524,0-35.048,0-52.572c5.225,0,10.446,0,15.671,0c1.175-4.216,0.17-10.613,0.504-15.671c-4.383-1.175-10.95-0.169-16.175-0.504c0,5.392,0,10.784,0,16.175c-17.524,0-35.048,0-52.572,0c0-5.391,0-10.783,0-16.175c17.524,0,35.048,0,52.572,0c0-11.458,0-22.916,0-34.374c5.392,0,10.784,0,16.175,0c0,5.225,0,10.446,0,15.671c5.057,1.176,12.298,0.169,18.199,0.504c0-5.392,0-10.784,0-16.176c5.225,0,10.447,0,15.671,0c1.179-16.512,0.167-35.216,0.504-52.572c-5.224,0-10.446,0-15.671,0c-1.176,5.057-0.169,12.298-0.504,18.199c-11.457,0-22.916,0-34.374,0c0-6.066,0-12.132,0-18.199c11.458,0,22.916,0,34.374,0c0-11.458,0-22.916,0-34.374c-5.897,0-11.795,0-17.692,0c-1.176,5.057-0.169,12.297-0.505,18.199c-5.392,0-10.784,0-16.175,0c0-11.626,0-23.253,0-34.88c16.849,0,33.7,0,50.549,0c0,11.626,0,23.253,0,34.88c5.898,0,11.795,0,17.692,0c1.178-10.617,0.168-23.421,0.505-34.88c5.392,0,10.783,0,16.175,0c0,17.019,0,34.037,0,51.056c-5.392,0-10.783,0-16.175,0c0,11.458,0,22.915,0,34.374c5.392,0,10.783,0,16.175,0c0,22.916,0,45.832,0,68.748c6.066,0,12.133,0,18.198,0c0-5.392,0-10.784,0-16.175c5.393,0,10.784,0,16.176,0c0,11.457,0,22.916,0,34.373c-16.681,0-33.362,0-50.044,0c-1.178,10.447-0.167,23.084-0.504,34.374c-6.066,0-12.133,0-18.198,0L188.13,206.538z M188.13,103.417c0,5.392,0,10.784,0,16.176c-5.393,0-10.784,0-16.176,0c0,11.458,0,22.916,0,34.374c5.392,0,10.784,0,16.176,0c0.337-11.291-0.674-23.926,0.504-34.374c5.897,0,11.795,0,17.692,0c0-5.392,0-10.783,0-16.176c-6.066,0-12.132,0-18.199,0H188.13z M222.503,206.538c0,5.393,0,10.784,0,16.176c-5.392,0-10.783,0-16.175,0c0,6.066,0,12.132,0,18.199c5.392,0,10.783,0,16.175,0c0,5.392,0,10.783,0,16.175c-11.457,0-22.915,0-34.373,0c0-11.458,0-22.916,0-34.373c6.066,0,12.132,0,18.198,0c0-5.393,0-10.784,0-16.176C211.72,206.539,217.111,206.539,222.503,206.538L222.503,206.538z M34.458,172.165c0-11.458,0-22.916,0-34.374c11.458,0,22.916,0,34.374,0c0,5.392,0,10.784,0,16.175c-6.066,0-12.132,0-18.198,0c0,5.897,0,11.795,0,17.692c10.447,1.178,23.083,0.168,34.373,0.505c0,5.392,0,10.783,0,16.176c5.897,0,11.795,0,17.692,0c1.176-4.384,0.169-10.951,0.504-16.176c5.392,0,10.784,0,16.176,0c0,5.392,0,10.783,0,16.176c-5.392,0-10.784,0-16.176,0c0,6.065,0,12.132,0,18.198c5.392,0,10.784,0,16.176,0c0,5.392,0,10.783,0,16.176c-28.308,0-56.616,0-84.923,0c0-5.393,0-10.784,0-16.176c5.225,0,10.446,0,15.671,0c0.842-10.28,0.842-24.094,0-34.374c-5.225,0-10.446,0-15.671,0L34.458,172.165z"/>',
  },
})
// reward 所需图标
Icon.register({
  reward_arrow_l: {
    width: 360,
    height: 360,
    raw:
      '<path d="M83.956,179.999L263.954,0l12.09,12.093L108.141,180l167.903,167.906L263.954,360L96.047,192.09L83.956,179.999z"/>',
  },
})
Icon.register({
  reward_arrow_r: {
    width: 360,
    height: 360,
    raw:
      '<path d="M276.046,179.999L96.047,0L83.954,12.093L251.864,180L83.954,347.906L96.047,360l167.907-167.91L276.046,179.999z"/>',
  },
})
// 点赞
Icon.register({
  reward_fabulous: {
    width: 360,
    height: 360,
    raw:
      '<g id="icomoon-ignore"></g><path d="M43.573,164.075c6.078,0,10.996-4.927,10.996-10.997c0-6.074-4.918-11.008-10.996-11.008c-0.117,0-0.205,0.066-0.313,0.066l-26.291,0.062c-0.597-0.084-1.224-0.128-1.838-0.128c-8.275,0-14.985,6.789-14.985,15.196L0,339.559c0,8.398,6.719,14.974,15.006,14.974c0.808,0,1.605,0.128,2.379,0l25.674,0.02c0.04,0,0.062,0.017,0.101,0.017c0.04,0,0.059-0.017,0.106-0.017h0.308v-0.064c5.646-0.231,10.17-4.813,10.17-10.502c0-5.707-4.524-10.285-10.17-10.512v-0.165H22.139l0.609-169.234L43.573,164.075L43.573,164.075z M353.332,148.318c-8.112-13.225-19.911-18.585-34.19-19.158c-0.744-0.106-1.491-0.173-2.27-0.173l-86.42-0.292c5.658-16.427,9.542-35.997,9.542-54.401c0-11.925-1.344-23.536-3.808-34.723l-0.197,0.02c-4.479-19.593-21.77-34.203-42.477-34.203c-24.091,0-40.161,20.252-40.161,44.643c0,1.362-0.126,2.684,0,4.01c-1.261,45.598-38.39,82.261-82.559,87.294v23.095l-0.328,93.484v96.652l218.541-0.008c8.146,0.016,12.725-2.031,20.11-6.783c7.022-4.527,12.296-10.722,15.772-17.735c0.952-1.4,1.688-2.964,2.14-4.712l32.41-144.847c0.44-1.701,0.561-3.44,0.44-5.115c0.663-9.167-1.376-18.631-6.546-27.044l0,0l0,0L353.332,148.318z M339.586,170.57l-34.91,154.716l-0.044-0.016c-1.079,2.579-2.896,4.874-5.415,6.489c-1.756,1.147-3.695,1.833-5.654,2.088c-0.622-0.08-1.275,0-1.935,0l-200.68-0.227l-0.079-171.389c37.58-16.944,65.145-33.518,79.342-73.062c0.036,0.012,0.055,0.02,0.087,0.03c1.26-3.841,2.652-8.719,3.65-13.945c2.344-12.284,2.214-24.456,2.214-24.456c-2.074-15.975,10.921-22.282,18.665-22.282c10.641,0.362,21.147,14.161,21.147,22.013c0,0,2.347,11.597,2.371,24.059c0.02,15.718-1.963,23.916-1.963,23.916h-0.193c-2.476,12.986-6.821,25.297-12.813,36.589l0.148,0.139c-0.988,2.027-1.571,4.301-1.571,6.707c0,8.386,8.04,9.147,16.319,9.147l100.443,0.115c0,0,6.17,0.189,6.179,0.189v0.046c5.103-0.268,10.188,2.189,13.084,6.903c2.322,3.792,2.718,8.248,1.471,12.192l0.133,0.037L339.586,170.57z M74.971,354.606c0.132,0.008,0.185,0.008,0.122-0.013c0.103-0.02,0.151-0.027-0.122-0.027c-0.279,0-0.229,0.008-0.113,0.027C74.805,354.614,74.845,354.614,74.971,354.606L74.971,354.606L74.971,354.606z"/>',
  },
})
//游戏抽奖-- 大转盘·九宫格
Icon.register({
  gamelottery: {
    width: 1024,
    height: 1024,
    raw:
      '<g><path d="M512.008,0C229.22,0,0,229.26,0,512c0,282.738,229.221,512,512.009,512S1024,794.771,1024,512   C1024,229.231,794.781,0,512.008,0z M644.038,789.619c-117.489,55.879-257.43,31.75-349.423-60.245   c-91.991-91.997-116.113-231.942-60.227-349.428c55.885-117.486,179.673-187.071,309.089-173.75   c144.809,15.064,259.276,129.516,274.328,274.339C831.118,609.951,761.528,733.738,644.038,789.619z"/><path fill="#FF5B52" d="M783.693,439.167h0.016c-12.831-47.681-37.975-91.148-72.913-126.042l-4.598-4.585L583.86,430.92   c-10.931-9.664-23.689-17.038-37.52-21.679l44.816-167.211l-6.431-1.709c-47.671-12.75-97.856-12.75-145.528,0l-6.32,1.698   L477.6,409.216c-13.833,4.635-26.587,12.009-37.508,21.678L317.739,308.648l-4.582,4.612   c-34.942,34.883-60.08,78.355-72.887,126.042l-1.697,6.32l167.211,44.803c-2.912,14.313-2.912,29.063,0,43.373l-167.211,44.803   l1.697,6.323c3.342,12.495,7.559,24.742,12.619,36.648c14.1,33.43,34.57,63.795,60.268,89.404l4.582,4.586l122.475-122.38   c10.914,9.688,23.676,17.068,37.518,21.694l-44.828,167.209l6.336,1.684c47.716,12.657,97.92,12.657,145.637,0l6.308-1.697   L546.378,614.81c13.827-4.652,26.575-12.033,37.492-21.705l122.432,122.434l4.603-4.65c34.867-34.943,59.97-78.416,72.804-126.082   l1.682-6.326l-167.274-44.8c2.908-14.317,2.908-29.068,0-43.387l167.274-44.802L783.693,439.167z M588.874,532.99   c-9.533,34.821-41.275,58.89-77.376,58.673c-36.104-0.221-67.554-24.67-76.666-59.603c-9.111-34.933,6.385-71.631,37.779-89.457   c8.26-4.627,17.284-7.752,26.639-9.263l3.021-0.535l9.984-52.369l10.787,52.57l2.994,0.587c9.196,1.63,18.042,4.84,26.143,9.49   C583.358,461.288,598.41,498.171,588.874,532.99z"/></g>',
  },
})
// 快捷键
Icon.register({
  wall_key: {
    width: 34,
    height: 34,
    raw:
      '<circle cx="17" cy="17" r="17"/><path fill="#FFFFFF" d="M13.425,6.575h10.429l-3.63,8.127H26c0,0.154-13.648,14.722-13.648,14.722l2.862-11.194H10L13.425,6.575z"/>',
  },
})
// 互动召集
Icon.register({
  wall_zhao: {
    width: 34,
    height: 34,
    raw:
      '<path d="M19.3,17.1c-1.9,0.1-4.1,0.7-6.6,1.7c0,0.2,0.1,0.6,0.2,1.1c0.3,1.4,0.4,2.4,0.5,3c0.5,0,1.1-0.2,2-0.3 c1.5-0.2,3.4-0.4,5.7-0.7c0.2-1,0.3-1.9,0.2-2.7C21.3,17.8,20.6,17.2,19.3,17.1z"/> <path d="M17,0C7.6,0,0,7.6,0,17s7.6,17,17,17s17-7.6,17-17S26.4,0,17,0z M9.5,12.2c-0.6-0.8-0.1-1.5,1.3-2.2 c2.3-0.9,5.2-1.4,8.7-1.5c2.5,0.1,3.8,0.9,4,2.5c0.2,1.3-0.2,2.7-1.1,4.2c-0.7,0-1.6,0.2-2.8,0.5c0.8-1.1,1.2-2.4,1.4-3.8 c0.2-1.6-0.4-2.4-1.8-2.4c-0.7,0-1.3,0.1-2.1,0.2c-0.1,1.1-0.9,3.2-2.4,6.3c-0.7,0-1.6,0.2-2.7,0.5c1.4-2.7,2.3-4.7,2.7-6.1 c-0.8,0.3-1.9,0.8-3.3,1.6c-0.1,0.1-0.2,0.1-0.3,0.2C10.4,12.8,9.8,12.8,9.5,12.2z M11.5,25.5c-0.2-0.7-0.4-1.8-0.6-3.3 c-0.3-1.7-0.5-2.9-0.6-3.4c-0.3-1.2,0-1.8,0.9-1.9c0.4-0.2,0.8,0,1.1,0.3c2.5-0.7,4.8-1,6.7-0.9c2,0,3.4,0.5,4.1,1.4 c0.7,0.8,0.8,2.1,0.5,3.9c0.1,0,0.3,0,0.5-0.1c0.2,0,0.4,0,0.5,0l0.3,0.7C19.6,23,15.2,24.1,11.5,25.5z"/>',
  },
})
// 二维码
Icon.register({
  wall_moveqrcode: {
    width: 34,
    height: 34,
    raw:
      '<circle cx="17" cy="17" r="17"/><g><path fill="#FFFFFF" d="M16.188,16.188v-1.625V9.688V8.063l0,0h-1.625l0,0H9.688l0,0H8.063l0,0v1.625v4.875v1.625l0,0H16.188   L16.188,16.188z M9.688,9.688h4.875v4.875H9.688V9.688z"/><circle fill="#FFFFFF" cx="12.125" cy="12.125" r="1.625"/><path fill="#FFFFFF" d="M16.188,25.938v-1.625v-4.875v-1.625l0,0h-1.625l0,0H9.688l0,0H8.063l0,0v1.625v4.875v1.625l0,0H16.188   L16.188,25.938z M9.688,19.438h4.875v4.875H9.688V19.438z"/><circle fill="#FFFFFF" cx="12.125" cy="21.875" r="1.625"/><path fill="#FFFFFF" d="M24.313,8.063h-4.875l0,0h-1.625l0,0v1.625v4.875v1.625l0,0h8.125l0,0v-1.625V9.688V8.063l0,0H24.313   L24.313,8.063z M24.313,14.563h-4.875V9.688h4.875V14.563z"/><circle fill="#FFFFFF" cx="21.875" cy="12.125" r="1.625"/><rect x="24.313" y="24.313" fill="#FFFFFF" width="1.625" height="1.625"/><rect x="21.063" y="24.313" fill="#FFFFFF" width="1.625" height="1.625"/><polygon fill="#FFFFFF" points="17,4 14.563,6.438 19.438,6.438  "/><polygon fill="#FFFFFF" points="17,30 19.438,27.563 14.563,27.563  "/><polygon fill="#FFFFFF" points="27.563,14.563 27.563,19.438 30,17  "/><polygon fill="#FFFFFF" points="6.438,19.438 6.438,14.563 4,17  "/><polygon fill="#FFFFFF" points="24.313,20.25 22.688,20.25 22.688,17.813 19.438,17.813 19.438,17.813 17.813,17.813    17.813,17.813 17.813,21.875 17.813,25.938 19.438,25.938 19.438,21.875 25.938,21.875 25.938,17.813 24.313,17.813  "/></g>',
  },
})
// 翻页按钮
Icon.register({
  wall_topswtich: {
    width: 34,
    height: 34,
    raw:
      '<path fill="#D7DFE4" d="M18,17c0,1.657-1.343,3-3,3H3c-1.657,0-3-1.343-3-3V3c0-1.657,1.343-3,3-3h12c1.657,0,3,1.343,3,3V17z"/><polygon fill="#617584" points="14,12.5 9,7.5 4,12.5 "/>',
  },
})
Icon.register({
  wall_bottomswtich: {
    width: 34,
    height: 34,
    raw:
      '<path fill="#D7DFE4" d="M18,17c0,1.657-1.343,3-3,3H3c-1.657,0-3-1.343-3-3V3c0-1.657,1.343-3,3-3h12c1.657,0,3,1.343,3,3V17z"/><polygon fill="#617584" points="4,8.5 9,13.5 14,8.5 "/>',
  },
})
Icon.register({
  wall_music: {
    width: 72,
    height: 72,
    raw:
      '<circle cx="35.999" cy="36" r="36"/><g><g><path fill="#FFFFFF" d="M38.435,14.155c-0.328-0.122-0.676-0.184-1.025-0.184c-0.861,0-1.682,0.374-2.248,1.026L23.819,25.021 h-5.757c-3.158,0-5.728,2.569-5.728,5.728V41.8c0,3.158,2.569,5.727,5.728,5.727h5.758l11.342,10.024 c0.566,0.652,1.387,1.027,2.248,1.027c0.355,0,0.7-0.063,1.025-0.185c1.16-0.432,1.938-1.549,1.938-2.781V16.935 C40.368,15.696,39.589,14.578,38.435,14.155L38.435,14.155z M15.501,30.743c0-1.412,1.148-2.56,2.56-2.56h7.183L37.2,17.386 v37.765L25.245,44.354h-7.183c-1.412,0-2.56-1.148-2.56-2.56V30.743z M47.374,25.052v-0.006l-0.06-0.059 c-0.301-0.297-0.696-0.46-1.115-0.46c-0.422,0-0.82,0.166-1.12,0.466c-0.302,0.3-0.466,0.698-0.466,1.121 c0,0.422,0.164,0.82,0.466,1.121c0.011,0.01,0.021,0.019,0.03,0.026c2.03,2.049,3.147,4.758,3.147,7.632 c0,2.865-1.111,5.568-3.128,7.614c-0.02,0.011-0.036,0.024-0.05,0.039c-0.302,0.3-0.466,0.699-0.466,1.122 c0,0.422,0.164,0.821,0.466,1.121c0.295,0.297,0.696,0.463,1.114,0.461c0.351,0,0.683-0.113,0.957-0.321l0.02,0.021l0.145-0.145 c2.646-2.658,4.104-6.179,4.104-9.913C51.419,31.188,49.982,27.697,47.374,25.052L47.374,25.052z"/><path fill="#FFFFFF" d="M37.409,58.993c-0.972,0-1.899-0.418-2.544-1.147l-11.204-9.902h-5.6c-3.387,0-6.144-2.756-6.144-6.144 V30.749c0-3.388,2.756-6.144,6.144-6.144h5.6l11.204-9.902c0.645-0.73,1.572-1.148,2.544-1.148c0.4,0,0.794,0.071,1.17,0.21 c1.315,0.482,2.204,1.756,2.211,3.168v38.679c0,1.404-0.888,2.679-2.21,3.17C38.209,58.923,37.814,58.993,37.409,58.993z  M18.062,25.437c-2.929,0-5.312,2.383-5.312,5.312V41.8c0,2.929,2.382,5.312,5.312,5.312h5.915l11.481,10.146l0.018,0.021 c0.487,0.562,1.193,0.883,1.934,0.883c0.305,0,0.602-0.053,0.88-0.157c0.999-0.372,1.669-1.333,1.669-2.392V16.935 c-0.005-1.064-0.675-2.026-1.667-2.39c-0.284-0.105-0.58-0.158-0.882-0.158c-0.74,0-1.446,0.321-1.934,0.883l-0.018,0.021 l-0.021,0.019L23.977,25.437H18.062z M37.617,56.088L25.085,44.771h-7.023c-1.641,0-2.976-1.336-2.976-2.976V30.743 c0-1.641,1.335-2.976,2.976-2.976h7.023L37.617,16.45V56.088z M18.062,28.599c-1.183,0-2.144,0.962-2.144,2.145v11.052 c0,1.182,0.961,2.144,2.144,2.144h7.343l11.38,10.276V18.322l-11.38,10.277H18.062z M46.189,45.666 c-0.524,0-1.036-0.213-1.405-0.584c-0.378-0.378-0.587-0.881-0.587-1.414c0-0.534,0.209-1.037,0.588-1.416 c0.023-0.023,0.049-0.045,0.074-0.064c1.923-1.965,2.981-4.554,2.981-7.295c0-2.754-1.068-5.352-3.007-7.32 c-0.016-0.012-0.031-0.027-0.045-0.041c-0.383-0.382-0.592-0.885-0.592-1.419s0.209-1.037,0.588-1.416s0.88-0.587,1.414-0.587 c0.53,0,1.03,0.206,1.407,0.58l0.183,0.182v0.01c2.61,2.708,4.046,6.255,4.046,10.01c0,3.846-1.501,7.47-4.225,10.205 l-0.443,0.444l-0.082-0.084c-0.273,0.137-0.577,0.208-0.892,0.208H46.189L46.189,45.666z M45.36,42.853 c-0.213,0.22-0.332,0.509-0.332,0.815c0,0.312,0.122,0.605,0.345,0.826c0.215,0.217,0.512,0.341,0.816,0.341v0.415l0.003-0.415 c0.259,0,0.503-0.082,0.705-0.237l0.152-0.116c2.549-2.575,3.952-5.979,3.952-9.589c0-3.591-1.394-6.983-3.924-9.549 l-0.114-0.116c-0.213-0.185-0.482-0.286-0.765-0.286c-0.311,0-0.605,0.122-0.826,0.344c-0.223,0.222-0.345,0.516-0.345,0.827 c0,0.307,0.119,0.595,0.333,0.815l0.018,0.012l0.026,0.027c2.107,2.127,3.268,4.941,3.268,7.925c0,2.975-1.154,5.782-3.249,7.906 l-0.035,0.038L45.36,42.853z"/></g><g><path fill="#FFFFFF" d="M53.497,18.965c-0.013-0.029-0.032-0.055-0.055-0.078c-0.3-0.3-0.698-0.466-1.12-0.466 c-0.424,0-0.822,0.166-1.121,0.466c-0.619,0.618-0.619,1.624,0,2.242l0.001,0.002c3.441,3.796,5.337,8.685,5.337,13.768 c0,5.092-1.901,9.988-5.354,13.79c-0.601,0.617-0.595,1.613,0.015,2.222c0.3,0.301,0.698,0.465,1.122,0.465 c0.422,0,0.82-0.164,1.119-0.465c0.037-0.036,0.06-0.075,0.072-0.101c0.003-0.005,0.004-0.008,0.004-0.009 c3.991-4.396,6.189-10.045,6.189-15.909C59.707,29.013,57.501,23.357,53.497,18.965L53.497,18.965z"/><path fill="#FFFFFF" d="M52.322,51.792c-0.534,0-1.037-0.209-1.416-0.587c-0.768-0.768-0.777-2.022-0.024-2.8 c3.38-3.725,5.241-8.521,5.241-13.507c0-4.977-1.855-9.766-5.226-13.484c-0.372-0.376-0.576-0.876-0.576-1.406 c0-0.535,0.207-1.038,0.585-1.416c0.379-0.379,0.882-0.587,1.416-0.587c0.533,0,1.035,0.208,1.413,0.587 c0.04,0.04,0.073,0.082,0.101,0.127c4.055,4.464,6.286,10.206,6.286,16.173c0,5.95-2.224,11.683-6.262,16.15 c-0.024,0.041-0.064,0.103-0.126,0.163C53.357,51.584,52.855,51.792,52.322,51.792z M52.322,18.837 c-0.313,0-0.605,0.122-0.828,0.344c-0.221,0.22-0.342,0.514-0.342,0.827c0,0.313,0.121,0.606,0.342,0.827l0.02,0.021 c3.507,3.87,5.441,8.857,5.441,14.043c0,5.195-1.94,10.192-5.463,14.07l-0.009,0.011c-0.443,0.454-0.438,1.188,0.01,1.637 c0.223,0.222,0.516,0.344,0.828,0.344c0.308,0,0.599-0.119,0.818-0.336l0.018-0.046l0.053-0.058 c3.921-4.318,6.08-9.868,6.08-15.628c0-5.775-2.166-11.332-6.101-15.647l-0.041-0.044l-0.017-0.036 C52.915,18.955,52.627,18.837,52.322,18.837z"/></g></g>',
  },
})
//订货会
Icon.register({
  wall_placeorder: {
    width: 34,
    height: 34,
    raw: `<svg version="1.1" id="图层_1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px"viewBox="0 0 1024 1024" style="enable-background:new 0 0 1024 1024;" xml:space="preserve"><style type="text/css">.st09888{fill:#2ABE5B;}
   </style><g><path class="st09888" d="M457.4,422.7c-7.8-2.1-16.2,0.1-22,5.9l-48.2,48.2h0l-16-16c-5.7-5.7-14.1-8-22-5.9c-7.8,2.1-14,8.2-16.1,16.1c-2.1,7.8,0.1,16.2,5.9,22l32.2,32.1c4.2,4.3,10,6.7,16.1,6.7c6,0,11.8-2.4,16.1-6.7l64.3-64.3c5.7-5.7,8-14.1,5.9-22C471.3,430.9,465.2,424.8,457.4,422.7z"/><path class="st09888" d="M457.4,559c-7.8-2.1-16.2,0.1-22,5.9l-48.2,48.2h0l-16-16c-8.9-8.9-23.3-8.9-32.2,0c-8.9,8.9-8.9,23.3,0,32.2l32.2,32.1c4.2,4.3,10,6.7,16.1,6.7c6,0,11.8-2.4,16.1-6.7l64.3-64.3c5.7-5.7,8-14.1,5.9-22C471.3,567.3,465.2,561.1,457.4,559z"/><path class="st09888" d="M673.2,603.7H536.8c-12.6,0-22.7,10.2-22.7,22.7c0,12.6,10.2,22.7,22.7,22.7h136.3c12.6,0,22.7-10.2,22.7-22.7C695.9,613.9,685.7,603.7,673.2,603.7z"/><path class="st09888" d="M512,0C229.2,0,0,229.2,0,512s229.2,512,512,512s512-229.2,512-512S794.8,0,512,0z M468.7,240.1h90.9c25.1,0,45.5,20.3,45.5,45.4c0,25.1-20.4,45.4-45.5,45.4h-90.9c-25.1,0-45.4-20.3-45.4-45.4C423.2,260.5,443.6,240.1,468.7,240.1zM786.8,694.6c0,50.2-40.7,90.9-90.9,90.9H332.3c-50.2,0-90.9-40.7-90.9-90.9V376.5c0-50.2,40.7-90.9,90.9-90.9h45.5c0,50.2,40.7,90.9,90.9,90.9h90.9c50.2,0,90.9-40.7,90.9-90.9h45.4c50.2,0,90.9,40.7,90.9,90.9V694.6z"/><path class="st09888"d="M673.2,467.4H536.8c-12.6,0-22.7,10.2-22.7,22.7c0,12.5,10.2,22.7,22.7,22.7h136.3c12.6,0,22.7-10.2,22.7-22.7C695.9,477.5,685.7,467.4,673.2,467.4z"/></g></svg>`,
  },
})
//启动仪式
Icon.register({
  wall_launchingceremony: {
    width: 1024,
    height: 1024,
    raw: `<svg version="1.1" id="图层_1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px"viewBox="0 0 1024 1024" style="enable-background:new 0 0 1024 1024;" xml:space="preserve"><style type="text/css">.stlaunchingceremony0{fill:#30B15A;}.stlaunchingceremony1{fill:#FFFFFF;}</style><path class="stlaunchingceremony0" d="M512,1024L512,1024C229.2,1024,0,794.8,0,512v0C0,229.2,229.2,0,512,0h0c282.8,0,512,229.2,512,512v0C1024,794.8,794.8,1024,512,1024z"/><g><path class="stlaunchingceremony1" d="M719.8,240.7H289.2c-28.6,0-51.9,23.2-51.9,51.9v430.6c0,28.6,23.2,51.9,51.9,51.9h248.3c10.5,0,16.3-12.3,9.6-20.4c-24.2-28.9-37.7-67-34.2-108.4c6-71.9,63.5-129.5,135.4-135.6c39-3.3,75.2,8.4,103.4,30c8.2,6.3,20.1,0.6,20.1-9.8V292.5C771.7,263.9,748.5,240.7,719.8,240.7z M541.7,480.6c0,15-12.3,27.2-27.2,27.2H341.8c-15,0-27.2-12.3-27.2-27.2v0c0-15,12.3-27.2,27.2-27.2h172.7C529.5,453.4,541.7,465.7,541.7,480.6L541.7,480.6z M593.6,363.8c0,15-12.3,27.2-27.2,27.2H341.8c-15,0-27.2-12.3-27.2-27.2v0c0-15,12.3-27.2,27.2-27.2h224.5C581.3,336.6,593.6,348.9,593.6,363.8L593.6,363.8z"/><path class="stlaunchingceremony1" d="M662.3,534.8c-69.5-0.6-125.9,55.8-125.3,125.3c0.6,67.5,55.7,122.6,123.2,123.2c69.5,0.6,125.9-55.8,125.3-125.3C784.9,590.5,729.8,535.3,662.3,534.8z M713.5,673.9l-66.2,38.2c-11.5,6.6-25.8-1.7-25.8-14.9v-76.4c0-13.2,14.3-21.5,25.8-14.9l66.2,38.2C725,650.8,725,667.3,713.5,673.9z"/></g></svg>`,
  },
})
// 盲盒、红包墙
Icon.register({
  wall_blindbox: {
    width: 1024,
    height: 1024,
    raw: `
      <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="1024" height="1024" viewBox="0 0 1024 1024">
        <defs>
          <clipPath id="clip-path">
            <rect id="SVGID" width="525.84" height="510.395" fill="#fff"/>
          </clipPath>
        </defs>
        <g id="组_109" data-name="组 109" transform="translate(-1306.447 -332.947)">
          <g id="编组" transform="translate(1306.447 332.947)">
            <g id="红包墙" transform="translate(0 0)">
              <circle id="椭圆形" cx="512" cy="512" r="512" fill="#fe5b52"/>
            </g>
          </g>
          <g id="组_82" data-name="组 82" transform="translate(1552.75 579.25)">
            <g id="组_81" data-name="组 81" transform="translate(0 0)">
              <g id="组_80" data-name="组 80" clip-path="url(#clip-path)">
                <path id="路径_21" data-name="路径 21" d="M829.012,422.426V615.871a4.115,4.115,0,0,1-4.1,4.1h-24.63a4.115,4.115,0,0,1-4.1-4.1V422.426H582.717V619.975a32.87,32.87,0,0,0,32.84,32.839H1009.63a32.834,32.834,0,0,0,32.838-32.839V422.426Zm180.618-180.1H988.08a4.106,4.106,0,0,1-3.385-6.414c8-11.9,11.9-24.987,11.08-37.97C994.8,182.442,987,169,973.3,159.044c-9.851-7.13-22.938-12.314-36.79-14.674-25.142-4.207-51.775,2.924-75.068,20.166a155.457,155.457,0,0,0-34.073,35.507,16.411,16.411,0,0,1-26.732,0c-10.159-14.366-21.552-26.272-34.072-35.507-23.242-17.243-49.877-24.425-75.018-20.166-13.853,2.36-26.939,7.544-36.79,14.674-13.7,9.9-21.5,23.349-22.474,38.895-.821,12.983,3.027,26.067,11.085,37.97a4.094,4.094,0,0,1-3.388,6.414H615.557A65.674,65.674,0,0,0,549.88,308V373.68a16.414,16.414,0,0,0,12.315,15.907h500.8a16.414,16.414,0,0,0,12.314-15.907V308a65.673,65.673,0,0,0-65.677-65.68Zm-166.2-6.106c27.3-49.515,61.883-63.831,87.587-59.469,3.132.514,30.788,5.7,31.968,23.3.977,15.341-15.958,35.663-45.153,42.28H847.025a4.115,4.115,0,0,1-3.593-6.106ZM665.023,200.042c1.13-17.6,28.785-22.731,31.967-23.3,25.758-4.31,60.343,9.954,87.59,59.469a4.109,4.109,0,0,1-3.593,6.106H710.177c-29.146-6.67-46.129-26.939-45.154-42.28Zm0,0" transform="translate(-549.956 -143.062)" fill="#fff"/>
              </g>
            </g>
          </g>
        </g>
      </svg>`,
  },
})

// 签名墙
Icon.register({
  wall_signature: {
    width: 1024,
    height: 1024,
    raw: `<svg version="1.1" id="图层_1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px"viewBox="0 0 1024 1024" style="enable-background:new 0 0 1024 1024;" xml:space="preserve">
   <style type="text/css">
     .signature_0{display:none;fill:#FF5B52;}
     .signature_1{fill:#41A1FF;}
     .signature_2{fill:#FFFFFF;}
   </style>
   <path class="signature_0" d="M512.1,0C229.3,0,0.1,229.2,0.1,512s229.2,512,512,512s512-229.2,512-512S794.9,0,512.1,0z M771.3,752.7l-1.9,3.2c-7,10.2-16,16-26.9,16H265.7c-12.8,0-21.1-3.2-26.9-9.6c-1.9-1.9-3.2-4.5-3.8-5.8l-1.3-2.6v-2.6c0.6-147.3,120-266.6,267.1-267.5l22.4-65.2c-28-19.4-46.3-51.7-46.3-88.5c0-59.5,48-107.5,107.5-107.5s107.5,48,107.5,107.5s-48,107.5-107.5,107.5c-0.9,0-1.8,0-2.7,0L563.4,491C681,518.5,769.6,623.8,771.3,748.9V752.7z"/>
   <circle class="signature_1" cx="512" cy="512" r="512"/>
   <path class="signature_2" d="M717.6,776H303.2c-34,0-61.8-27.8-61.8-61.8V299.8c0-34,27.8-61.8,61.8-61.8h414.4c34,0,61.8,27.8,61.8,61.8v414.4C779.4,748.2,751.6,776,717.6,776z"/>
   <path class="signature_1" d="M534.6,644.5l-2.6,5.1c-6.8,10.3-12,21-16.3,32.5c-5.6,15.8-2.6,30.4,14.1,36c29.1,9.4,60.8,7.7,89-3.9c7.7-2.6,9.4-3.4,28.7-10.7c13.7-5.6,28.3-10.7,42.8-14.6c0.9,0,1.7-0.4,2.6-0.9c8.6-1.7,5.6-4.7,14.1,0.9c3,2.6,7.3,3.4,11.1,2.6c3.9-0.9,4.7-2.6,6.9-6.4c4.7-6.9,3-16.3-3.9-21c-15.8-10.7-18.8-8.6-34.7-5.1c-1.3,0.4-2.6,0.4-3.4,0.9
     c-15.8,4.3-31.3,9.4-46.7,15.4c-18.4,7.3-20.1,8.1-27.4,10.3c-20.6,8.6-43.7,10.7-65.5,5.1c3.9-9.4,8.6-18.4,14.1-27
     c1.3-1.7,2.1-3.4,3-5.1c7.3-11.6,12.8-24.4,15.4-37.7c3.4-16.3-6.4-32.1-22.7-35.5c-0.4,0-1.3-0.4-1.7-0.4
     c-23.1-5.6-49.7,2.6-124.6,31.7c-68.5,27-98.5,35.1-107.9,30c-3.4-2.1-7.3-2.6-11.1-1.3c-8.1,2.1-12.8,10.7-10.7,18.4
     c0.9,3.8,3.4,6.9,6.9,9c19.3,7.3,40.7,6.4,59.5-1.7c24.8-7.7,49.7-16.3,73.6-27c65.1-25.7,92.9-33.8,106.2-30.4
     c2.6,0.9,2.6,0,2.1,2.6C543.6,626.5,539.7,635.9,534.6,644.5L534.6,644.5z M611.2,300.7c-16.3-15-41.5-14.6-57.4,0.9L316.2,538.3c-1.7,1.7-3,3.9-3.9,6.4l-18.8,67.6c-2.1,8.1,2.6,16.3,10.7,18c3,0.9,6.4,0.4,9.4-0.4l59.5-24.8c4.7-2.1,9-4.7,12.8-8.6l237.2-225.6l2.1-2.1c15.4-16.3,15-42-1.7-57.4l-0.9-0.9L611.2,300.7z M611.2,300.7"/>
   </svg>`,
  },
})

// 射击
Icon.register({
  wall_fire: {
    width: 44,
    height: 44,
    raw: `<svg width="44px" height="44px" viewBox="0 0 44 44" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
      <!-- Generator: Sketch 59 (86127) - https://sketch.com -->
      <title>编组 2</title>
      <desc>Created with Sketch.</desc>
      <g id="射击游戏后台" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
          <g id="选择模式备份-2" transform="translate(-1102.000000, -744.000000)">
              <g id="编组-2" transform="translate(1102.000000, 744.000000)">
                  <circle id="椭圆形" fill="#FBBA1C" cx="22" cy="22" r="22"></circle>
                  <g id="编组" transform="translate(9.000000, 9.000000)">
                      <circle id="椭圆形" fill="#FFFFFF" cx="11" cy="15" r="11"></circle>
                      <circle id="椭圆形" stroke="#FBBA1C" stroke-width="2" fill="#FFFFFF" cx="11" cy="15" r="8"></circle>
                      <circle id="椭圆形" stroke="#FBBA1C" stroke-width="2" fill="#FFFFFF" cx="11" cy="15" r="4"></circle>
                      <line x1="11.6875" y1="15.25" x2="21.3125" y2="7.75" id="直线-8" stroke="#FBBA1C" stroke-width="2" stroke-linecap="round"></line>
                      <polygon id="路径" fill="#FFFFFF" fill-rule="nonzero" points="23.0063762 10 19.2337938 9.28906532 19 5.48177553 24.9369465 0 24.8342189 4.26199928 29 4.46770119 23.0063762 10"></polygon>
                  </g>
              </g>
          </g>
      </g>
  </svg>`,
  },
})

// 接金币
Icon.register({
  wall_goldcoin: {
    width: 1024,
    height: 1024,
    raw: `<svg version="1.1" id="图层_1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px"
      viewBox="0 0 1024 1024" style="enable-background:new 0 0 1024 1024;" xml:space="preserve">
   <style type="text/css">
     .st0goldcoin{fill:#FFBA1F;}
     .st1goldcoin{display:none;}
     .st2goldcoin{fill:#FFFFFF;}
   </style>
   <circle class="st0goldcoin" cx="512" cy="512" r="512"/>
   <g id="_x34_34qLW.tif" class="st1goldcoin">
       <image style="display:inline;overflow:visible;" width="277" height="252" id="Layer_0" xlink:href="36A8E93E79A08612.png"  transform="matrix(0.75 0 0 0.75 -264.938 377.5951)">
     </image></g><g id="ILNJhv.tif" class="st1goldcoin"><image style="display:inline;overflow:visible;" width="242" height="224" id="Layer_0_1_" xlink:href="36A8E93E79A08615.png"  transform="matrix(0.75 0 0 0.75 -462.8344 392.529)">
     </image></g><g>
     <path class="st2goldcoin" d="M776,662.4c-28.4,16.3-98.4,52.4-98.4,52.4h-76.1l-63.4-49.1c0,0,77.9,7.9,108.6,9.2c41.2,1.8,43.3-48.1,18.4-58.6c-27.6-11.6-181.3-82-253.1-82.1c-36-0.1-114.5,54.9-160.5,89.7c-7.4-9.9-21.1-12.6-31.4-5.7L181,644.5c-10.9,7.3-14.1,22.6-7.1,34l68.7,111.6c7,11.4,21.7,14.7,32.6,7.4l39.2-26.3c8.4-5.6,12.2-16,10.3-25.6c29.8-10.6,70.8-24.8,84.9-27.4c8.4-1.6,132.1,74.6,158.6,78.1c19.2,2.5,130.2-7.7,130.2-7.7s36.9-28,115.1-71.8C851.9,695.3,820.2,637.1,776,662.4z"/>
     <path class="st2goldcoin" d="M716.7,603.5v-162c0-12.8-10-23.3-22.3-23.3H354.6c-12.3,0-22.3,10.5-22.3,23.3v106.7c31-19.1,61.1-33.8,79.7-33.8c71.7,0.2,225.4,70.6,253.1,82.1c11.3,4.7,30.3,17.6,29.3,30.1C706.7,626.8,716.7,616.3,716.7,603.5z"/><path class="st2goldcoin" d="M311.2,403.9h429.2c3.5,0,6.4-3,6.4-6.7v-67.7c0-3.7-2.9-6.7-6.4-6.7H311.2c-3.5,0-6.4,3-6.4,6.7v67.7C304.8,400.9,307.7,403.9,311.2,403.9z"/>
     <path class="st2goldcoin" d="M430.2,304.7L430.2,304.7c20.7,11.4,45.1,14.2,64,14.2c10.1,0,18.6-0.8,24.2-1.5c4.3-0.6,17.8-0.6,22.1,0c5.5,0.7,14.1,1.5,24.2,1.5c18.9,0,43.3-2.8,64-14.2h0c17.4-9.6,28.9-26.2,31.6-45.6c2.7-19.9-4.2-40.4-18.1-53.6c-13.8-13.1-35.3-19.7-56.1-17.2c-20.5,2.5-38.1,13.4-48.3,30c-3.5,5.6-6.2,11.6-8.3,17.7c-2.1-6.1-4.8-12-8.3-17.7c-10.2-16.6-27.8-27.5-48.3-30c-20.8-2.5-42.3,4-56.1,17.2c-13.9,13.2-20.8,33.7-18.1,53.6C401.3,278.5,412.8,295.1,430.2,304.7zM558.4,229.9c8.8-14.3,23-17.9,30.6-18.9c13.6-1.7,27.4,2.4,36.1,10.7c8.7,8.3,13,21.5,11.2,34.5c-1,7.2-4.9,20.6-19.8,28.8h0c-23.4,12.9-55,11.6-68.9,10.3C546.3,282,545,251.6,558.4,229.9z M433.7,221.7c8.7-8.2,22.5-12.3,36.1-10.7c7.7,0.9,21.8,4.6,30.6,18.9c13.4,21.8,12.1,52.1,10.7,65.4c-13.9,1.4-45.5,2.6-68.9-10.3h0c-15-8.2-18.9-21.6-19.8-28.8C420.7,243.2,425,229.9,433.7,221.7z"/></g>
   </svg>`,
  },
})

// 地图签到
Icon.register({
  wall_mapsign: {
    width: 46,
    height: 46,
    raw: `<svg width="46px" height="46px" viewBox="0 0 46 46" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>编组 22</title>
    <g id="页面-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="编组-22">
            <circle id="椭圆形备份" fill="#27BF5B" cx="23" cy="23" r="23"></circle>
            <g id="ditu-mian备份-2" transform="translate(11.000000, 11.000000)" fill="#FFFFFF" fill-rule="nonzero">
                <path d="M11.3044336,0 C8.15802135,0 5.60721374,2.61436865 5.60721374,5.8398921 C5.60539359,7.04718766 5.97024565,8.22512043 6.65113274,9.21018515 L11.3044336,15.6359683 L15.9563499,9.21018515 C16.6371386,8.22508673 17.0018931,7.04715674 16.9999926,5.8398921 C17.0010928,2.61436865 14.4505689,0 11.3044336,0 Z M11.3044336,8.29189171 C10.1322634,8.29200652 9.07545038,7.56824295 8.62680267,6.45810307 C8.17815496,5.34796319 8.42603002,4.07007989 9.2548399,3.2203498 C10.0836498,2.3706197 11.3301672,2.11639014 12.413122,2.57621226 C13.4960767,3.03603438 14.2021898,4.11935002 14.2021898,5.32099244 C14.2020369,6.96155309 12.9047568,8.2914511 11.3044336,8.29160785 L11.3044336,8.29189171 Z M3.02402251,8.06111193 C2.72865372,7.62376502 2.18667606,7.43948944 1.69490356,7.60920369 L0.803009365,7.91719392 C0.323423348,8.08226334 0.000210007763,8.54282594 0,9.06144148 L0,22.8017796 C0.000249189954,23.1877608 0.181737413,23.5500174 0.487618314,23.7750824 C0.793499215,24.0000276 1.18636558,24.0604967 1.54316248,23.9372275 L7.12324687,22.0109402 L7.12324687,13.4627212 L3.02402251,8.06111193 Z" id="形状"></path>
                <path d="M11.7091048,18.413766 L9,14.7378443 L9,21.7012596 L15.413539,23.981728 L15.413539,13.1655648 L11.7091048,18.413766 Z M22.4442046,5.06515715 L21.0396904,5.56469113 C20.798255,5.65041468 20.5895877,5.81635648 20.4448931,6.03770118 L16.7186878,11.316847 C16.7186878,11.3304037 16.7217581,11.3430762 16.7217581,11.3569276 L16.7217581,24 L23.1972609,21.697723 C23.6767831,21.5270858 24,20.7269473 24,20.1923428 L24,6.2439984 C23.9996541,5.84324373 23.8166463,5.4671605 23.5082805,5.23350872 C23.1999147,4.9999732 22.8038878,4.9372 22.4442046,5.06515715 Z" id="形状"></path>
            </g>
        </g>
      </g>
    </svg>`,
  },
})

// 对对碰
Icon.register({
  wall_supperzzle: {
    width: 1024,
    height: 1024,
    raw: `<svg version="1.1" id="图层_1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px"
    viewBox="0 0 46 46" style="enable-background:new 0 0 46 46;" xml:space="preserve">
 <g id="编组备份">
 <circle id="椭圆形备份" :fill="fillColor" cx="23" cy="23" r="23"></circle>
 <g id="图层_1_1_"  fill="#FFFFFF" fill-rule="nonzero">
     <path  d="M25.6,9.4c-4.3,0-8,2.4-9.9,6c-3.5,1.9-6,5.6-6,9.9c0,6.2,5,11.2,11.2,11.2c4.3,0,8-2.4,9.9-6
       c3.5-1.9,6-5.6,6-9.9C36.8,14.4,31.8,9.4,25.6,9.4z M27.6,32c-0.9,0.9-1.9,1.6-3,2c-1.2,0.5-2.4,0.7-3.7,0.7s-2.5-0.3-3.7-0.7
       c-1.1-0.5-2.1-1.2-3-2c-0.9-0.9-1.6-1.9-2-3c-0.5-1.2-0.7-2.4-0.7-3.7c0-1.3,0.3-2.5,0.7-3.7c0.5-1.1,1.2-2.1,2-3l0,0
       c0.1-0.1,0.4,0,0.3,0.2c-0.1,0.6-0.2,1.3-0.2,1.9c0,6.2,5,11.2,11.2,11.2c0.7,0,1.3-0.1,1.9-0.2C27.7,31.6,27.8,31.9,27.6,32
       L27.6,32z M32.3,27.4L32.3,27.4c-0.1,0.1-0.4,0-0.3-0.2c0.1-0.6,0.2-1.3,0.2-1.9c0-6.2-5-11.2-11.2-11.2c-0.7,0-1.3,0.1-1.9,0.2
       c-0.2,0-0.3-0.2-0.2-0.3l0,0c0.9-0.9,1.9-1.6,3-2c1.2-0.5,2.4-0.7,3.7-0.7s2.5,0.3,3.7,0.7c1.1,0.5,2.1,1.2,3,2
       c0.9,0.9,1.6,1.9,2,3c0.5,1.2,0.7,2.4,0.7,3.7s-0.3,2.5-0.7,3.7C33.9,25.5,33.2,26.5,32.3,27.4L32.3,27.4z"/>
   </g>
 </g>
 </svg>`,
  },
})
// 业绩目标会
Icon.register({
  wall_performance: {
    width: 46,
    height: 46,
    raw: `<svg width="46px" height="46px" viewBox="0 0 46 46" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>编组备份</title>
    <g id="页面-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="编组备份">
            <circle id="椭圆形备份" fill="#27BF5B" cx="23" cy="23" r="23"></circle>
            <g id="yejijiangli备份" transform="translate(11.000000, 11.000000)" fill="#FFFFFF" fill-rule="nonzero">
                  <path d="M19.7252053,10.6713037 C20.0484247,10.3395101 20.562635,10.3062536 20.9241084,10.5937648 L21.0095081,10.6713037 L23.7338778,13.4688592 C23.8752082,13.6140908 23.9654192,13.8027074 23.991881,14.0060932 L24,14.1282478 L24,23.067379 C24,23.2959957 23.9182088,23.5166124 23.7705636,23.687075 C23.6229416,23.8576668 23.4191602,23.9667451 23.1980249,23.9935384 L23.0918768,24 L1.29541558,24 C1.07289531,24 0.858193336,23.9159995 0.69220523,23.7643063 C0.525816533,23.612339 0.419592187,23.40327 0.393607109,23.1766104 L0.387292329,23.067379 L0.387292329,22.5544528 C0.387292329,22.3273745 0.467880749,22.1076808 0.614323141,21.9372183 L0.692505951,21.8572178 L7.92140747,15.2584085 C8.24905423,14.9597819 8.73866951,14.9441014 9.08392549,15.2211775 L9.16571673,15.2956395 L11.5514948,17.7455005 C11.874655,18.077428 12.3889667,18.110691 12.7503979,17.8230394 L12.8357975,17.7455005 L19.7249046,10.6713037 L19.7252053,10.6713037 Z M23.7428989,0.75555129 C23.7651509,0.873398169 23.7651509,0.994321965 23.7428989,1.11186113 L22.7758379,6.21835367 C22.7116366,6.55616656 22.471065,6.83047106 22.1497741,6.93220415 C21.8283397,7.03330485 21.478625,6.94372504 21.2416508,6.69958734 L20.5217677,5.96019826 L12.8357975,13.8550154 C12.5127949,14.1871313 11.9984734,14.220659 11.6368944,13.9331697 L11.5514948,13.8550154 L8.49364538,10.7159194 L2.80524563,16.0565672 C2.44734143,16.3933374 1.8934963,16.3811178 1.55011107,16.0288747 L0.266109012,14.7100975 C-0.0887030039,14.3455885 -0.0887030039,13.7561369 0.266109012,13.391628 L7.98515651,6.17558416 C8.3437742,5.84004309 8.89719471,5.85361661 9.23968967,6.20635359 L12.1937965,9.23806429 L17.9534629,3.3220284 L17.2362861,2.58540855 C16.9981154,2.34140502 16.9109205,1.9828598 17.009556,1.65309519 C17.1076527,1.32393851 17.3748664,1.07676688 17.7047815,1.01001436 L22.6778087,0.0173929676 C23.0386398,-0.0550995221 23.4064695,0.102992881 23.6084846,0.417395379 C23.6746393,0.519549842 23.7200455,0.634012068 23.7428989,0.754012819 L23.7428989,0.755858979 L23.7428989,0.75555129 Z" id="形状"></path>
              </g>
          </g>
      </g>
  </svg>`,
  },
})

// 团队答题
Icon.register({
  wall_teamanswer: {
    width: 200,
    height: 200,
    raw: `<svg width="200px" height="200px" viewBox="0 0 200 200" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
      <!-- Generator: Sketch 59 (86127) - https://sketch.com -->
      <title>抢答备份</title>
      <desc>Created with Sketch.</desc>
      <g id="抢答备份" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
          <circle id="椭圆形" fill="#FBBA1C" cx="100" cy="100" r="100"></circle>
          <g id="抢答" transform="translate(43.000000, 27.000000)" fill="#FFFFFF" fill-rule="nonzero">
              <path d="M107.679411,74.9650349 L94.8570483,87.9720279 C91.532732,91.3286713 87.5299837,91.3286713 85.155472,88.5314686 C83.5950785,86.6433567 84.2056673,80.9090909 84.6805696,77.2727272 L91.1256728,23.6363637 C91.7362616,18.8111889 88.344102,14.1958042 83.5950785,13.6363637 C78.9138984,13.0769231 74.5719341,16.7132868 74.0291885,21.5384616 L66.9056534,63.6363636 C66.6342806,64.3356643 65.4131032,46.0139859 63.242121,8.67132861 C63.3099643,3.91608396 59.4429024,0 54.6938789,0 C49.9448555,0 46.0777936,3.91608396 46.0777936,8.74125875 L45.9421072,63.916084 C45.9421072,64.3356644 43.2283796,50 37.6652379,20.9790209 C36.8511196,16.2237761 32.3056258,12.7972027 27.6922887,13.6363637 C23.0111085,14.4755245 19.8903216,19.3006993 20.7722831,24.0559441 L27.5566023,74.4755245 C27.5566023,75.1048952 23.9609131,65.944056 16.7016917,47.1328672 C15.1412983,42.5874127 10.1887454,39.9300699 5.7110947,41.5384616 C1.23344403,43.1468532 -1.07322443,48.3916084 0.487168872,53.006993 L18.3299283,112.237762 L18.6013011,113.216783 C23.7573836,129.230769 38.2758266,140 54.6938789,140 C65.684476,139.93007 76.4037003,136.223776 85.2233151,129.510489 C94.4499892,123.846154 123.961777,91.6783216 123.961777,91.6783217 C128.439428,87.0629371 127.760996,80.1398603 123.283346,75.5944056 C118.737852,70.8391608 112.157062,70.4195804 107.679411,74.9650349 L107.679411,74.9650349 Z" id="路径"></path>
          </g>
      </g>
  </svg>`,
  },
})

// 许愿树
Icon.register({
  wall_wish: {
    width: 1024,
    height: 1024,
    raw: `<svg viewBox="0 0 46 46" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <!-- Generator: Sketch 59 (86127) - https://sketch.com -->
    <title>图标1备份</title>
    <desc>Created with Sketch.</desc>
    <g id="图标1备份" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <circle id="椭圆形" fill="#41A1FF" cx="23" cy="23" r="23"></circle>
        <polygon id="路径" fill="#FFFFFF" fill-rule="nonzero" points="37 30.8181647 29.9576638 21.5250376 33.821224 21.5250376 22.5 6 11.178776 21.5250376 15.0423362 21.5250376 8 30.8181647 20.6563504 30.8181647 20.6563504 39 24.3416236 39 24.3416236 30.8181647"></polygon>
    </g>
  </svg>`,
  },
})
//签到簿
Icon.register({
  wall_signbook: {
    width: 1024,
    height: 1024,
    raw: `
    <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 46 46" fill="none"><g opacity="1"  transform="translate(0 0)"><g opacity="1"  transform="translate(0 0)"><path id="圆形 1" fill-rule="evenodd" style="fill:#41A1FF" opacity="1" d="M23 0C10.3 0 0 10.3 0 23C0 35.7 10.3 46 23 46C35.7 46 46 35.7 46 23C46 10.3 35.7 0 23 0Z"></path><g opacity="1"  transform="translate(9 10)"><mask id="bg-mask-0" fill="white"><use transform="translate(0 0)" xlink:href="#path_0"></use></mask><g mask="url(#bg-mask-0)" ><path id="路径 1" fill-rule="evenodd" style="fill:#FFFFFF" opacity="1" d="M22.7509 3.49941L5.25383 3.49941C3.80513 3.49941 2.62926 4.67528 2.62926 6.12395L2.62926 20.1216C2.62926 21.5703 3.80513 22.7462 5.25383 22.7462L11.0156 22.7462L13.3862 25.1167C13.5555 25.2861 13.7813 25.3754 14.007 25.3754C14.2328 25.3754 14.4539 25.2908 14.6279 25.1167L16.9985 22.7462L22.7603 22.7462C24.209 22.7462 25.3848 21.5703 25.3848 20.1216L25.3848 6.12395C25.3754 4.67528 24.1949 3.49941 22.7509 3.49941ZM9.18596 13.9976C8.4616 13.9976 7.87368 13.4097 7.87368 12.6854C7.87368 11.961 8.4616 11.3731 9.18596 11.3731C9.9103 11.3731 10.4982 11.961 10.4982 12.6854C10.4982 13.4097 9.9103 13.9976 9.18596 13.9976ZM14.4351 13.9976C13.7107 13.9976 13.1228 13.4097 13.1228 12.6854C13.1228 11.961 13.7107 11.3731 14.4351 11.3731C15.1594 11.3731 15.7473 11.961 15.7473 12.6854C15.7473 13.4097 15.1594 13.9976 14.4351 13.9976ZM19.6889 13.9976C18.9646 13.9976 18.3766 13.4097 18.3766 12.6854C18.3766 11.961 18.9646 11.3731 19.6889 11.3731C20.4132 11.3731 21.0012 11.961 21.0012 12.6854C21.0012 13.4097 20.4085 13.9976 19.6889 13.9976Z"></path></g></g></g></g><defs><rect id="path_0" x="0" y="0" width="28" height="28" rx="0" ry="0"/></defs></svg>`,
  },
})

//拍照抽奖
Icon.register({
  wall_photolottery: {
    width: 47,
    height: 47,
    raw: `<svg width="47px" height="47px" viewBox="0 0 47 47" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <!-- Generator: Sketch 59 (86127) - https://sketch.com -->
    <title>拍照抽奖</title>
    <desc>Created with Sketch.</desc>
    <g id="页面-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="活动总览" transform="translate(-1142.000000, -644.000000)">
            <g id="拍照抽奖" transform="translate(1142.000000, 644.000000)">
                <circle id="椭圆形" fill="#FF5B52" cx="23.5" cy="23.5" r="23.5"></circle>
                <path d="M28,23.5 C28,25.425 26.425,27 24.5,27 C22.575,27 21,25.425 21,23.5 C21,21.575 22.575,20 24.5,20 C26.425,20 28,21.575 28,23.5 Z M37,17.5454545 L37,31.1818182 C37,32.1818182 36.1642857,33 35.1428571,33 L12.8571429,33 C11.8357143,33 11,32.1818182 11,31.1818182 L11,17.5454545 C11,16.5454545 11.8357143,15.7272727 12.8571429,15.7272727 L17.7089286,15.7272727 L20.0535714,13 L27.4821429,13 L29.8267857,15.7272727 L35.1428571,15.7272727 C36.1642857,15.7272727 37,16.5454545 37,17.5454545 Z M30,23.5 C30,20.4520833 27.5479167,18 24.5,18 C21.4520833,18 19,20.4520833 19,23.5 C19,26.5479167 21.4520833,29 24.5,29 C27.5479167,29 30,26.5479167 30,23.5 Z" id="形状" fill="#FFFFFF" fill-rule="nonzero"></path>
            </g>
        </g>
    </g>
  </svg>`,
  },
})

//九宫格
Icon.register({
  wall_ninegrids: {
    width: 47,
    height: 47,
    raw: `<svg width="47px" height="47px" viewBox="0 0 47 47" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <!-- Generator: Sketch 59 (86127) - https://sketch.com -->
    <title>编组 3</title>
    <desc>Created with Sketch.</desc>
    <g id="页面-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="活动总览" transform="translate(-330.000000, -802.000000)">
            <g id="编组-3" transform="translate(330.000000, 802.000000)">
                <circle id="椭圆形" fill="#FF5B52" cx="23.5" cy="23.5" r="23.5"></circle>
                <g id="编组-2" transform="translate(12.000000, 13.000000)" fill="#FFFFFF">
                    <rect id="矩形" x="0" y="0" width="6" height="6" rx="1"></rect>
                    <rect id="矩形备份-5" x="0" y="8" width="6" height="6" rx="1"></rect>
                    <rect id="矩形备份-8" x="0" y="16" width="6" height="6" rx="1"></rect>
                    <rect id="矩形备份-3" x="8" y="0" width="6" height="6" rx="1"></rect>
                    <rect id="矩形备份-6" x="8" y="8" width="6" height="6" rx="1"></rect>
                    <rect id="矩形备份-9" x="8" y="16" width="6" height="6" rx="1"></rect>
                    <rect id="矩形备份-4" x="16" y="0" width="6" height="6" rx="1"></rect>
                    <rect id="矩形备份-7" x="16" y="8" width="6" height="6" rx="1"></rect>
                    <rect id="矩形备份-10" x="16" y="16" width="6" height="6" rx="1"></rect>
                </g>
            </g>
        </g>
    </g>
  </svg>`,
  },
})

//盲盒
Icon.register({
  wall_mysterybox: {
    width: 47,
    height: 47,
    raw: `
    <svg width="47px" height="47px" viewBox="0 0 47 47" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <!-- Generator: Sketch 59 (86127) - https://sketch.com -->
    <title>编组 4</title>
    <desc>Created with Sketch.</desc>
    <g id="页面-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="活动总览" transform="translate(-734.000000, -802.000000)">
            <g id="编组-4" transform="translate(734.000000, 802.000000)">
                <circle id="椭圆形备份-2" fill="#FF5B52" cx="23.5" cy="23.5" r="23.5"></circle>
                <path d="M24.5,19.9990645 C23.9473196,20.0086162 23.3970677,19.9231815 22.872893,19.7464322 L13.8480826,16.3889858 C13.1090392,16.113165 13,15.7128588 13,15.4980603 C13,15.2832619 13.1102507,14.8878374 13.8480826,14.6071349 L22.872893,11.2533497 C23.3971123,11.076786 23.9473325,10.9913562 24.5,11.0007174 C25.0526788,10.9911896 25.6029267,11.0766237 26.127107,11.2533497 L35.1519174,14.6107962 C35.8909608,14.886617 36,15.2869232 36,15.5017217 C36,15.7165201 35.8897493,16.1119446 35.1519174,16.3926471 L26.127107,19.7500936 C25.6027183,19.9256001 25.0524717,20.0097958 24.5,19.9990645 Z M21.5192138,35.9999307 C21.2943809,35.9976281 21.0717631,35.9546295 20.8619198,35.8729024 L12.6771034,32.7116881 C11.6597131,32.2495654 11.0037061,31.2275707 11,30.0989254 L11,19.5615443 C11,18.6119104 11.5017512,17.9999806 12.2769568,17.9999806 C12.4918908,18.0035113 12.7044035,18.0465284 12.9041458,18.126937 L21.2031106,21.2868818 C22.2509296,21.7345065 22.949899,22.7533858 22.9981255,23.9034531 L22.9981255,34.4408343 C23.0185485,34.8494726 22.8713798,35.2485162 22.5913975,35.5436652 C22.3114152,35.8388143 21.9234609,36.0038814 21.5192138,35.9999307 L21.5192138,35.9999307 Z M26.2196546,35.9896148 L26.2635658,35.9896148 C25.4806908,35.9896148 25.0001762,35.5360007 25.0001762,34.5967375 L25.0001762,23.9611517 C24.9877453,22.8250291 25.6340721,21.7888354 26.6474757,21.3201948 L34.8224975,18.1282376 C35.0463319,18.0458534 35.2821464,18.0025351 35.5200592,17.9999806 C35.9241598,17.9949242 36.3123226,18.1608676 36.5923257,18.4585022 C36.8723288,18.7561369 37.019179,19.1588959 36.9979867,19.5710894 L36.9979867,30.2066752 C36.9367656,31.371301 36.2284633,32.3974408 35.1750421,32.8476321 L26.8808327,35.9486101 C26.6643996,36.0014629 26.4403525,36.0136209 26.2196546,35.9844892 L26.2196546,35.9896148 Z" id="形状" fill="#FFFFFF" fill-rule="nonzero"></path>
            </g>
        </g>
    </g>
</svg>
    `,
  },
})
//红包墙
Icon.register({
  wall_packetwall: {
    width: 47,
    height: 47,
    raw: `<svg width="47px" height="47px" viewBox="0 0 47 47" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <!-- Generator: Sketch 59 (86127) - https://sketch.com -->
    <title>编组 5</title>
    <desc>Created with Sketch.</desc>
    <g id="页面-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="活动总览" transform="translate(-1142.000000, -802.000000)">
            <g id="编组-5" transform="translate(1142.000000, 802.000000)">
                <circle id="椭圆形备份-3" fill="#FF5B52" cx="23.5" cy="23.5" r="23.5"></circle>
                <path d="M26.1423674,28 C26.6142041,28 27,28.32495 27,28.7219205 L27,32.2780794 C27,32.6753358 26.6138571,33 26.1423674,33 L10.8576327,33 C10.3857959,33 10,32.67505 10,32.2780794 L10,28.7219205 C10,28.3246642 10.3861429,28 10.8576327,28 L26.1423674,28 Z M33.9996455,28 L33.1086687,30.3079565 L33.1086687,30.6920435 L34,33 L28.8913313,33 C28.39922,33 28,32.6554093 28,32.2306812 L28,32.2306812 L28,28.7690326 C28,28.3443045 28.39922,28 28.8906222,28 L28.8906222,28 L33.9996455,28 Z M34.8783784,28 C35.3632432,28 35.7567568,28.3444038 35.7567568,28.7691208 L35.7567568,28.7691208 L35.7567568,32.2308792 C35.7567568,32.6555962 35.3632432,33 34.8783784,33 L34.8783784,33 L34,30.6920658 L34,30.3076483 Z M30.2219465,29.525186 C30.1272824,29.5217516 29.9996455,29.5895821 29.9996455,29.6868918 L29.9996455,29.6868918 L29.9996455,30.2887808 C29.9996455,30.2887808 29.8361993,30.2876359 29.7681262,30.2913566 C29.6160255,30.2993703 29.5539798,30.385518 29.551498,30.5005724 C29.5490161,30.6156268 29.6606985,30.6971952 29.7603262,30.696909 C29.8096791,30.6967373 29.8859635,30.6961534 29.9390945,30.6957138 L29.9996455,30.6951918 C29.9996455,30.6951918 30.0007091,31.1960504 29.9996455,31.290498 C29.9985818,31.3849456 30.1099096,31.4559245 30.2219465,31.4522038 C30.3343379,31.4484831 30.4204928,31.3898111 30.4204928,31.2461362 L30.4204928,31.2461362 L30.4204928,30.69834 L30.8945222,30.7017745 L30.8959404,31.2598741 C30.8945223,31.3995421 30.9781954,31.4493417 31.0795958,31.4519176 C31.1675235,31.453921 31.333097,31.3823698 31.3366425,31.2564396 L31.3366425,31.2564396 L31.3366425,30.69834 L31.4926432,30.69834 C31.4926431,30.69834 31.9939727,31.1219233 32.109555,31.231826 C32.2258465,31.3414425 32.3166105,31.3692043 32.4102109,31.3013738 C32.5059387,31.2326846 32.5002543,31.1119061 32.4151746,31.0231826 C32.3286651,30.9333142 31.8631448,30.5008586 31.8631448,30.5008586 C32.0424926,30.3514614 32.2203627,30.2009128 32.3967382,30.0492272 C32.4772203,29.9768174 32.5555752,29.8236978 32.4470839,29.7412708 C32.3343379,29.6551231 32.1928736,29.722095 32.1311824,29.7759015 C32.0702003,29.8297081 31.5007977,30.2945049 31.5007977,30.2945049 L31.5007977,30.2945049 L31.3295515,30.2936462 L31.3295515,29.710933 C31.3295515,29.6247854 31.1519234,29.5486548 31.0579684,29.5515169 C30.9640135,29.5543789 30.8902677,29.6196337 30.8902677,29.7106468 C30.8902677,29.8019462 30.8945222,30.2913566 30.8945222,30.2913566 L30.8945222,30.2913566 L30.4169474,30.2902118 L30.4169474,29.6868918 C30.4169474,29.6007441 30.316256,29.5286205 30.2219465,29.525186 Z M35.1423104,21 C35.6141266,21 36,21.3251424 36,21.722219 L36,25.277781 C36,25.6443132 35.6712085,25.9495524 35.2495289,25.9943435 L35.1423104,26 L35.1423104,26 L19.8576896,26 C19.3858734,26 19,25.6748576 19,25.277781 L19,21.7219313 C19,21.4775765 19.1462613,21.2606809 19.3686434,21.1299853 L19.4554678,21.0849071 C19.5455865,21.0442934 19.6451766,21.01684 19.7505395,21.0056517 L19.8576896,21 L35.1423104,21 Z M17.1088235,21 C17.6011765,21 18,21.3443841 18,21.7690769 L18,21.7690769 L18,25.2309231 C18,25.6556159 17.6008235,26 17.1088235,26 L17.1088235,26 L12,26 L12.8911765,23.6921978 L12.8911765,23.3075164 L12,21 Z M11,21 L12,23.3076703 L12,23.6920439 L11,25.9997142 C10.448,26 10,25.6556356 10,25.2309671 L10,25.2309671 L10,21.7690329 C10,21.3443644 10.448,21 11,21 L11,21 Z M15.7778824,22.5478708 C15.6652941,22.5513004 15.5791765,22.6101743 15.5791765,22.7536439 L15.5791765,22.7536439 L15.5791765,23.3012289 L15.1051765,23.298371 L15.1037647,22.7399257 C15.1051765,22.6001715 15.0211765,22.550443 14.9202353,22.5478708 C14.832,22.5455845 14.6668236,22.6173192 14.6625882,22.7433553 L14.6625882,22.7433553 L14.6625882,23.3015147 L14.5069412,23.3015147 C14.5069412,23.3015147 14.0054118,22.8779651 13.89,22.7679337 C13.7738823,22.6584739 13.6831765,22.6307517 13.5892941,22.6984853 C13.493647,22.7670763 13.4996471,22.8882538 13.5847059,22.9768505 C13.6708235,23.0665905 14.1363529,23.4992855 14.1363529,23.4992855 C14.1363529,23.4992855 13.692,23.8705344 13.6027059,23.9511289 C13.5222353,24.0231495 13.4442353,24.1763361 13.5522353,24.2589311 C13.6655294,24.3449557 13.8067059,24.2780794 13.8684706,24.224064 C13.9295294,24.1703344 14.4988235,23.7056302 14.4988235,23.7056302 L14.4988235,23.7056302 L14.67,23.7064876 L14.67,24.2889397 C14.67,24.3749643 14.8475294,24.4512718 14.9417647,24.4484138 C15.0356471,24.4455559 15.1090588,24.3803944 15.1090588,24.2892255 C15.1090588,24.2206345 15.106875,23.9282652 15.1057831,23.7863675 L15.1051765,23.7084881 L15.5830588,23.7099171 L15.5830588,24.3129466 C15.5830588,24.3989711 15.6832941,24.4712775 15.7775294,24.4747071 C15.8721176,24.4781366 15.9998824,24.4101172 15.9998824,24.3129466 L15.9998824,24.3129466 L15.9998824,23.7113461 C15.9998824,23.7113461 16.1632941,23.7122035 16.2314118,23.7084881 C16.3835294,23.7004859 16.4456471,23.6141755 16.4481177,23.4992855 C16.4509412,23.3841097 16.3390588,23.3026579 16.2391765,23.3029437 C16.1898353,23.3031152 16.1135577,23.3038011 16.0604301,23.3043224 L15.9998824,23.3049443 L15.9998824,23.3049443 L15.9998824,22.7093455 C16.0009412,22.6147471 15.8897647,22.5438697 15.7778824,22.5478708 Z M26.1423674,14 C26.6142041,14 27,14.32495 27,14.7219205 L27,18.2780794 C27,18.6753358 26.6138571,19 26.1423674,19 L10.8576327,19 C10.3857959,19 10,18.67505 10,18.2780794 L10,14.7219205 C10,14.3246642 10.3861429,14 10.8576327,14 L26.1423674,14 Z M33.9996455,14 L33.1086687,16.3079565 L33.1086687,16.6920435 L34,19 L28.8913313,19 C28.39922,19 28,18.6554093 28,18.2306812 L28,18.2306812 L28,14.7690326 C28,14.3443045 28.39922,14 28.8906222,14 L28.8906222,14 L33.9996455,14 Z M34.8783784,14 C35.3632432,14 35.7567568,14.3444038 35.7567568,14.7691208 L35.7567568,14.7691208 L35.7567568,18.2308792 C35.7567568,18.6555962 35.3632432,19 34.8783784,19 L34.8783784,19 L34,16.6920658 L34,16.3076483 Z M30.2219465,15.525186 C30.1272824,15.5217516 29.9996455,15.5895821 29.9996455,15.6868918 L29.9996455,15.6868918 L29.9996455,16.2887808 C29.9996455,16.2887808 29.8361993,16.2876359 29.7681262,16.2913566 C29.6160255,16.2993703 29.5539798,16.385518 29.551498,16.5005724 C29.5490161,16.6156268 29.6606985,16.6971952 29.7603262,16.696909 C29.8096791,16.6967373 29.8859635,16.6961534 29.9390945,16.6957138 L29.9996455,16.6951918 C29.9996455,16.6951918 30.0007091,17.1960504 29.9996455,17.290498 C29.9985818,17.3849456 30.1099096,17.4559245 30.2219465,17.4522038 C30.3343379,17.4484831 30.4204928,17.3898111 30.4204928,17.2461362 L30.4204928,17.2461362 L30.4204928,16.69834 L30.8945222,16.7017745 L30.8959404,17.2598741 C30.8945223,17.3995421 30.9781954,17.4493417 31.0795958,17.4519176 C31.1675235,17.453921 31.333097,17.3823698 31.3366425,17.2564396 L31.3366425,17.2564396 L31.3366425,16.69834 L31.4926432,16.69834 C31.4926431,16.69834 31.9939727,17.1219233 32.109555,17.231826 C32.2258465,17.3414425 32.3166105,17.3692043 32.4102109,17.3013738 C32.5059387,17.2326846 32.5002543,17.1119061 32.4151746,17.0231826 C32.3286651,16.9333142 31.8631448,16.5008586 31.8631448,16.5008586 C32.0424926,16.3514614 32.2203627,16.2009128 32.3967382,16.0492272 C32.4772203,15.9768174 32.5555752,15.8236978 32.4470839,15.7412708 C32.3343379,15.6551231 32.1928736,15.722095 32.1311824,15.7759015 C32.0702003,15.8297081 31.5007977,16.2945049 31.5007977,16.2945049 L31.5007977,16.2945049 L31.3295515,16.2936462 L31.3295515,15.710933 C31.3295515,15.6247854 31.1519234,15.5486548 31.0579684,15.5515169 C30.9640135,15.5543789 30.8902677,15.6196337 30.8902677,15.7106468 C30.8902677,15.8019462 30.8945222,16.2913566 30.8945222,16.2913566 L30.8945222,16.2913566 L30.4169474,16.2902118 L30.4169474,15.6868918 C30.4169474,15.6007441 30.316256,15.5286205 30.2219465,15.525186 Z" id="形状结合" fill="#FFFFFF" fill-rule="nonzero"></path>
            </g>
        </g>
    </g>
  </svg>`,
  },
})

//名单抽奖
Icon.register({
  wall_listlottery: {
    width: 47,
    height: 47,
    raw: `<svg width="47px" height="47px" viewBox="0 0 47 47" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <!-- Generator: Sketch 59 (86127) - https://sketch.com -->
    <title>名单抽奖</title>
    <desc>Created with Sketch.</desc>
    <g id="页面-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="活动总览" transform="translate(-331.000000, -723.000000)">
            <g id="名单抽奖" transform="translate(331.000000, 723.000000)">
                <circle id="椭圆形" fill="#FF5B52" cx="23.5" cy="23.5" r="23.5"></circle>
                <path d="M26.2203264,12 C26.553907,12 26.7183482,12.338867 26.7183482,12.5059141 L26.7183482,12.5059141 L26.7183482,15.1977589 C26.7183482,17.2118697 28.3721563,18.5530193 30.1904055,18.5530193 L30.1904055,18.5530193 L30.1904055,18.5482465 L32.5019782,18.5482465 C32.8355589,18.5482465 33,18.8871135 33,19.0541606 L33,19.0541606 L33,32.651795 C33,33.9929446 32.0086548,35 30.6884273,35 L30.6884273,35 L16.3115727,35 C14.9913452,35 14,33.9929446 14,32.651795 L14,32.651795 L14,14.3529778 C14,13.0118282 14.9913452,12.0047728 16.3068744,12 L16.3068744,12 Z M29.5326409,29.625856 L17.4673591,29.625856 C17.1337784,29.625856 16.9693373,29.964723 16.9693373,30.1317701 L16.9693373,30.1317701 L16.9693373,30.4658643 C16.9693373,30.8047313 17.3029179,30.9717784 17.4673591,30.9717784 L17.4673591,30.9717784 L29.6970821,30.9717784 C29.8615232,30.9717784 30.0259644,30.8047313 30.0306627,30.4658643 L30.0306627,30.4658643 L30.0306627,30.1317701 C30.0306627,29.7929031 29.6970821,29.625856 29.5326409,29.625856 L29.5326409,29.625856 Z M29.5326409,25.5976344 L17.4673591,25.5976344 C17.1337784,25.5976344 16.9693373,25.9365013 16.9693373,26.1035485 L16.9693373,26.1035485 L16.9693373,26.4376427 C16.9693373,26.7765096 17.3029179,26.9435568 17.4673591,26.9435568 L17.4673591,26.9435568 L29.6970821,26.9435568 C29.8615232,26.9435568 30.0259644,26.7765096 30.0306627,26.4376427 L30.0306627,26.4376427 L30.0306627,26.1035485 C30.0306627,25.7646815 29.6970821,25.5976344 29.5326409,25.5976344 L29.5326409,25.5976344 Z M29.5326409,21.7364598 L17.4673591,21.7364598 C17.1337784,21.7364598 16.9693373,22.0753268 16.9693373,22.2423739 L16.9693373,22.2423739 L16.9693373,22.409421 C16.9693373,22.748288 17.3029179,22.9153351 17.4673591,22.9153351 L17.4673591,22.9153351 L29.6970821,22.9153351 C29.8615232,22.9153351 30.0259644,22.748288 30.0306627,22.5764681 L30.0306627,22.5764681 L30.0306627,22.2423739 C30.0306627,21.903507 29.6970821,21.7364598 29.5326409,21.7364598 L29.5326409,21.7364598 Z M28,12.7067522 C28,12.1495321 28.7567443,11.7780521 28.9445886,12.1495321 L28.9445886,12.1495321 L32.717576,15.880253 C33.2864759,16.2517331 32.9107873,17 32.3418873,17 L32.3418873,17 L30.6405545,17 C29.1324329,17 28,15.880253 28,14.389026 L28,14.389026 Z" id="形状结合" fill="#FFFFFF" fill-rule="nonzero"></path>
            </g>
        </g>
    </g>
  </svg>`,
  },
})

//名单抽奖
Icon.register({
  wall_seglottery: {
    width: 90,
    height: 90,
    raw: `<svg width="90px" height="90px" viewBox="0 0 90 90" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <!-- Generator: Sketch 59 (86127) - https://sketch.com -->
    <title>编组 12</title>
    <desc>Created with Sketch.</desc>
    <g id="图标" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="编组-12">
            <g id="编组-11">
                <circle id="椭圆形" fill="#FF5A53" cx="45" cy="45" r="45"></circle>
                <path d="M66.8888889,22 C70.2639624,22 73,25.5071028 73,29.8333333 L73,61.1666667 C73,65.4928972 70.2639624,69 66.8888889,69 L24.1111111,69 C20.7360376,69 18,65.4928972 18,61.1666667 L18,29.8333333 C18,25.5071028 20.7360376,22 24.1111111,22 L66.8888889,22 Z M52.047,25.916 L37.206,25.916 L37.206,65.083 L52.047,65.083 L52.047,25.916 Z M66.8888889,25.9166667 L55.103,25.916 L55.103,65.083 L66.8888889,65.0833333 C68.5764256,65.0833333 69.9444444,63.3297819 69.9444444,61.1666667 L69.9444444,29.8333333 C69.9444444,27.6702181 68.5764256,25.9166667 66.8888889,25.9166667 Z" id="形状结合备份" fill="#FFFFFF" fill-rule="nonzero"></path>
                <text id="8" font-family="MicrosoftYaHei-Bold, Microsoft YaHei" font-size="20" font-weight="bold" fill="#FF5A53">
                    <tspan x="21" y="53">8</tspan>
                </text>
                <text id="8备份-2" font-family="MicrosoftYaHei-Bold, Microsoft YaHei" font-size="19" font-weight="bold" fill="#FFFFFF">
                    <tspan x="57" y="61">8</tspan>
                </text>
                <text id="8备份" font-family="MicrosoftYaHei-Bold, Microsoft YaHei" font-size="19" font-weight="bold" fill="#FFFFFF">
                    <tspan x="38" y="53">8</tspan>
                </text>
                <text id="7" font-family="MicrosoftYaHei-Bold, Microsoft YaHei" font-size="19" font-weight="bold" fill="#FFFFFF">
                    <tspan x="56" y="40">7</tspan>
                </text>
            </g>
        </g>
    </g>
  </svg>`,
  },
})