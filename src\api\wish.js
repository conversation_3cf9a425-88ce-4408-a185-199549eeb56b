import api from './api'

export default {
  read: postData => api.fetchBaseData('/pro/hxc/web/prowish/read.htm', postData),
  updatestate: postData => api.fetchBaseData('/pro/hxc/web/prowish/updatestate.htm', postData),
  recordpage: postData => api.fetchBaseData('/pro/hxc/web/prowishrecord/page.htm', postData),
  recordranking: postData => api.fetchBaseData('/pro/hxc/web/prowishrecord/ranking.htm', postData),
  queryVoteById: postData => api.fetchBaseData('/pro/hxc/web/prowishrecord/queryVoteById.htm', postData),
}
