import api from './api'
export default {
  list: postData => api.fetchBaseData('/pro/hxc/web/proawards/list.htm', postData),
  recordlist: postData => api.fetchBaseData('/pro/hxc/web/proawardsrecord/list.htm', postData),
  recordpage: postData => api.fetchBaseData('/pro/hxc/web/proawardsrecord/page.htm', postData),
  recorddelete: postData => api.fetchBaseData('/pro/hxc/web/proawardsrecord/delete.htm', postData),
  leftCnt: postData => api.fetchBaseData('/pro/hxc/web/proawards/leftCnt.htm', postData),
  checkAwards: postData => api.fetchBaseData('/pro/hxc/web/proawards/checkAwards.htm', postData),
}
