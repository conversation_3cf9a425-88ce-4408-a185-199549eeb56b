import api from './api'
export default {
  // 当前轮活动信息
  read: postData => api.fetchBaseData('/pro/hxc/web/proshake/read.htm', postData),
  // 查询摇一摇排行榜
  ranking: postData => api.fetchBaseData('/pro/hxc/web/proshake/ranking.htm', postData),
  // 开始摇一摇
  go: postData => api.fetchBaseData('/pro/hxc/web/proshake/go.htm', postData),
  // 下一轮
  again: postData => api.fetchBaseData('/pro/hxc/web/proshake/again.htm', postData),
  // 剩余时间
  time: postData => api.fetchBaseData('/pro/hxc/web/proshake/time.htm', postData),
  // 轮次切换
  switch: postData => api.fetchBaseData('/pro/hxc/web/proshake/switch.htm', postData),
  themelist: postData => api.fetchBaseData('/pro/hxc/web/proshaketheme/list.htm', postData),
}
