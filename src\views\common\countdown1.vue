<template>
  <div class="countdown-mask-box">
    <transition-group tag="div" class="count-box" name="scale">
      <div v-for="item in allCount" :key="item.count" class="count" :style="{ fontSize: fontSize + 'px', color }" v-if="item.isShow">
        <icon v-if="config.style" :name="'count_down_' + config.style + '_' + item.count" scale="35"></icon>
        <template v-else>
          {{ item.count }}
        </template>
      </div>
    </transition-group>
  </div>
</template>
<script>
// 倒计时, 倒计时结束事件over
export default {
  props: {
    config: {
      type: Object,
      default() {
        return {
          start: 30,
        }
      },
    },
    go: {
      type: Boolean,
      default: true,
    },
    fontSize: {
      type: Number,
      default: 750,
    },
    color: {
      type: String,
      default: ' rgba(255, 255, 255, 0.2)',
    },
  },
  data() {
    return {
      allCount: [], // 显示的倒计时
      running: false, // 倒计时标记
      index: 0, // 索引

      forceOver: false,
    }
  },
  watch: {
    go(v) {
      if (v === true) {
        this.countDown.call(this)
      }
    },
  },
  methods: {
    next() {
      let prev = this.allCount[this.index - 1],
        next = this.allCount[this.index]
      if (!next || this.forceOver) {
        console.timeEnd()
        this.$emit('over')
        this.index = 0
        this.running = false
        return
      }
      if (document.visibilityState === 'visible') {
        prev && (prev.isShow = false)
        next && (next.isShow = true)
      } else {
        prev && (prev.isShow = false)
        next && (next.isShow = false)
      }
      let OT1 = setTimeout(() => {
        this.index++
        this.$emit('process', this.index)
        this.next.call(this)
        clearTimeout(OT1)
      }, 1000)
    },
    countDown() {
      if (this.running) {
        return
      }
      this.running = true
      this.index = 0
      console.time()
      this.next.call(this)
    },
    over() {
      // 主动停止倒计时
      this.forceOver = true
    },
  },
  mounted() {
    for (let i = this.config.start - 1; i > -1; i--) {
      this.allCount.push({
        count: i,
        isShow: false,
      })
    }
    if (this.go === true) {
      this.countDown.call(this)
    }
  },
}
</script>
<style scoped>
.countdown-mask-box {
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  z-index: 3;
  /* background-color: rgba(0, 0, 0, .6); */
}

.count-box {
  position: relative;
  width: 100%;
  height: 100%;
}

.count {
  position: absolute;
  width: 100%;
  height: 100%;
  left: 0;
  top: 0;
  display: flex;
  justify-content: center;
  align-items: center;
  font-family: Arial, Helvetica, sans-serif;
  font-weight: bold;
  text-align: center;
}

.scale-enter-active {
  transition: transform 0.5s;
}

.scale-leave-active {
  transition: transform 0.0001s;
}

.scale-enter,
.scale-leave-to {
  transform: scale(0, 0);
}
</style>
