<template>
  <div class="calendar-box" :style="'width:' + width + 'px;height:' + height + 'px;'">
    <div class="calendar-control">
      <span class="noselect" @click="prev">
        <i class="el-icon-arrow-left"></i>
      </span>
      <div>{{ headNowDate }}</div>
      <span class="noselect" @click="next">
        <i class="el-icon-arrow-right"></i>
      </span>
    </div>
    <div class="calendar-line" v-for="(line, index) in dataArr" :key="index">
      <span :class="{ on: checkOnBg(item) }" v-for="(item, index) in line" :key="index">{{ item }}</span>
    </div>
  </div>
</template>
<script>
import { Hi } from '@/libs/common'

export default {
  props: {
    onDay: {
      type: Object,
      default: {},
    },
    width: {
      default: 300,
    },
    height: {
      default: 300,
    },
  },
  created() {},
  data() {
    return {
      nowDate: new Date(),
      head: ['日', '一', '二', '三', '四', '五', '六'],
      dataArr: [],
    }
  },
  computed: {
    checkOnBg() {
      let _this = this
      return day => {
        let tmp = Hi.Date.format(new Date(this.nowDate.getFullYear(), this.nowDate.getMonth(), parseInt(day)), 'yyyy-MM-dd')
        return _this.onDay[tmp] ? true : false
      }
    },
    headNowDate() {
      return Hi.Date.format(this.nowDate, 'yyyy年  MM月')
    },
  },
  methods: {
    randerYearMonth() {
      let tempLine = [],
        year = this.nowDate.getYear(),
        month = this.nowDate.getMonth() - 1

      month = month - 1
      let getArr = () => {
        return ['', '', '', '', '', '', '']
      }
      let pushtempLine = line => {
        // 判断line中是否有满足条件的数据
        if (
          line.filter(item => {
            return item !== ''
          }).length
        ) {
          tempLine.push(line)
        }
      }
      pushtempLine(this.head)
      // 总天数
      let allDayCount = new Date(year, month + 1, 0).getDate()
      // 一号星期
      let firstDay = new Date(year, month, 1).getDay()
      let line = getArr()
      for (let day = 1; day <= allDayCount; day++) {
        // 获取当前星期，如果是星期日就新创建数组
        let nowDay = new Date(year, month, day).getDay()
        if (nowDay === 0) {
          pushtempLine(line)
          line = getArr()
        }
        line[nowDay] = day
      }
      pushtempLine(line)
      this.dataArr = tempLine
    },
    prev() {
      this.nowDate = Hi.Date.getDateOfPreMonth(this.nowDate)
      this.randerYearMonth.call(this)

      this.$emit('change', this.nowDate)
    },
    next() {
      this.nowDate = Hi.Date.getDateOfNextMonth(this.nowDate)
      this.randerYearMonth.call(this)

      this.$emit('change', this.nowDate)
    },
  },
  mounted() {
    this.randerYearMonth.call(this)
  },
}
</script>
<style scoped>
.calendar-box {
  display: flex;
  flex-direction: column;
}

.calendar-control {
  display: flex;
  width: 100%;
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 0 10px 0;
  border-bottom: 1px solid #dfe6ec;
}

.calendar-control span {
  display: block;
  text-align: center;
  cursor: pointer;
}

.calendar-line:nth-child(2) {
  font-weight: bold;
}

.calendar-line {
  flex: 1;
  display: flex;
  width: 100%;
}

.calendar-line span {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
}

.calendar-line span.on {
  background: url(@/assets/signpoint/sign-bg.png) center no-repeat;
}
</style>
