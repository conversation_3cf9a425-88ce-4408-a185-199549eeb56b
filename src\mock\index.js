import Mock from 'mockjs'

const requireAllModules = require.context('./', true, /\.js$/)
requireAllModules.keys().forEach(key => {
  let module = requireAllModules(key).default
  if (module && Object.prototype.toString.call(module) === '[object Array]') {
    module.forEach(item => {
      let urlReg = new RegExp('.*' + item.path, 'ig')
      Mock.mock(urlReg, options => {
        if (typeof item.data === 'function') {
          let reqObj = options.body.slice(5)
          reqObj = JSON.parse(decodeURIComponent(reqObj))
          let template = item.data(reqObj)
          return {
            data: Mock.mock(template),
          }
        } else {
          if (item.value) {
            return {
              data: item.value,
            }
          }
          return Mock.mock(item.data)
        }
      })
    })
  }
})

export default Mock
