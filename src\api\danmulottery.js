import api from './api'

export default {
  // 当前轮奖品信息
  read: postData => api.fetchBaseData('/pro/hxc/web/prodanmulottery/read.htm', postData),
  start: postData => api.fetchBaseData('/pro/hxc/web/prodanmulottery/start.htm', postData),
  stop: postData => api.fetchBaseData('/pro/hxc/web/prodanmulottery/stop.htm', postData),
  next: postData => api.fetchBaseData('/pro/hxc/web/prodanmulottery/next.htm', postData),
  // 切换
  switch: postData => api.fetchBaseData('/pro/hxc/web/prodanmulottery/switch.htm', postData),
  // 获取抽奖用户集合
  lotteryList: postData => api.fetchBaseData('/pro/hxc/web/prodanmulottery/lotteryList.htm', postData),
  // 获取中奖记录
  recordList: postData => api.fetchBaseData('/pro/hxc/web/prodanmulottery/recordList.htm', postData),
  recordDelete: postData => api.fetchBaseData('/pro/hxc/web/prodanmulottery/recordDelete.htm', postData),
  themelist: postData => api.fetchBaseData('/pro/hxc/web/prodanmulotterytheme/list.htm', postData),
}
