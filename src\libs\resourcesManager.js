import { Hi, timer } from '@/libs/common'
import * as THREE from 'three'
// 资源管理器
let resourcesManager = (v => {
  let dataObj = {}
  let loader = new THREE.TextureLoader()
  loader.crossOrigin = 'anonymous'
  return {
    dataObj,
    getResource(img) {
      return dataObj[img]
    },
    allTexture(filter = item => !item.isDefault && !item.isVirtual && !item.isFromOut) {
      return Object.values(dataObj).filter(filter)
    },
    // 异步纹理加载
    loadTexture(imgUrl, onLoad, onError, attr = { isDefault: false, isVirtual: false, isFromOut:false }, cache = true) {
      let tmp = dataObj[imgUrl]
      if (tmp) {
        setTimeout(v => onLoad && onLoad(tmp.texture), 0)
        return tmp.texture
      }
      let loadUrl = imgUrl.includes('qn/') && !imgUrl.includes('data:image/') ? `${imgUrl.split('?')[0]}?imageView2/1/w/128/h/128` : imgUrl
      return loader.load(
        loadUrl,
        texture => {
          texture.minFilter = THREE.LinearFilter
          if (cache) {
            dataObj[imgUrl] = {
              url: imgUrl,
              texture,
              image: texture.image,
              ...attr,
            }
          }
          texture.userData = Object.assign(texture.userData || {}, { imgUrl }, attr)
          onLoad && onLoad(texture)
        },
        xhr => {
          // 进度
        },
        onError,
      )
    },
    // 同步图片纹理加载(支持批量)
    async syncLoadTexture(img, ignoreErr, attr = { isDefault: false, isVirtual: false , isFromOut:false}, cache = true) {
      if (!img) {
        return
      }
      if (Hi.Type.isArray(img)) {
        let promiseArr = [],
          obj = {}
        img.forEach(item => (obj[item] = ''))
        Object.keys(obj).forEach(url => {
          let p = resourcesManager.syncLoadTexture(url, true, attr, cache)
          promiseArr.push(p)
        })
        try {
          await Promise.all(promiseArr)
        } catch (err) {
          console.error('加载失败', err)
        }
      } else {
        return new Promise((resolve, reject) => {
          resourcesManager.loadTexture(
            img,
            v => {
              resolve(v)
            },
            e => {
              if (ignoreErr) {
                resolve(e)
              } else {
                reject(e)
              }
            },
            attr,
            cache,
          )
        })
      }
    },
  }
})()
export { resourcesManager }

window.resourcesManager = resourcesManager
