import api from './api'

export default {
  // 当前轮活动信息
  read: postData => api.fetchBaseData('/pro/hxc/web/prowallredpack/read.htm', postData),
  // 再来一轮
  again: postData => api.fetchBaseData('/pro/hxc/web/prowallredpack/again.htm', postData),
  // 重新启动
  restart: postData => api.fetchBaseData('/pro/hxc/web/prowallredpack/restart.htm', postData),
  // 开始红包雨
  go: postData => api.fetchBaseData('/pro/hxc/web/prowallredpack/go.htm', postData),
  // 结束红包雨
  end: postData => api.fetchBaseData('/pro/hxc/web/prowallredpack/end.htm', postData),
  // 红包雨切换
  switch: postData => api.fetchBaseData('/pro/hxc/web/prowallredpack/switch.htm', postData),
  themelist: postData => api.fetchBaseData('/pro/hxc/web/prowallredpacktheme/list.htm', postData),
}
