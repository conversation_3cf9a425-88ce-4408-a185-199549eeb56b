<template>
  <div class="scrolltext-box" ref="scrollText">
    <p v-show="isScroll === true" class="scroll cont" ref="scrollCont">{{ cont }}</p>
    <p v-show="isScroll === false" class="cont">{{ cont }}</p>
    <div class="hidden limit" ref="hiddenText">{{ text }}</div>
  </div>
</template>
<script>
import { timer } from '@/libs/common'
export default {
  name: 'HiScrollText',
  props: {
    text: {
      type: String,
      require: true,
    },
    // width: {
    //   type: String,
    //   require: true,
    // },
  },
  data() {
    return {
      isScroll: null,
      interval: null,
      speed: 3,
    }
  },
  computed: {
    cont() {
      return this.isScroll ? this.text + this.text : this.text
    },
  },
  methods: {
    dealText() {
      // 判断字符宽度是否够显示
      const w = this.$refs.scrollText.getBoundingClientRect().width || 0,
        realW = this.$refs.hiddenText.getBoundingClientRect().width || 0
      if (w && realW > w) {
        // 不够，双倍内容，滚动
        this.isScroll = true
        clearInterval(this.interval)
        const dom = this.$refs.scrollCont,
          t = 10000 / w
        let n = 0
        this.interval = setInterval(() => {
          if (n < realW) {
            if (realW - n < 3) {
              n = realW
            } else {
              n += 3
            }
          } else if (n >= realW) {
            n = 0
          }
          dom.style.transformStyle = 'preserve-3d'
          dom.style.transform = `translate3d(-${n}px,0,0)`
        }, t)
      } else {
        // 够，直接显示
        this.isScroll = false
      }
    },
  },
  async mounted() {
    await timer(500)
    this.dealText()
  },
  destroyed() {
    clearInterval(this.interval)
  },
}
</script>
<style scoped lang="stylus">
.scrolltext-box
  position relative
  width 100%
  overflow hidden
  .cont
    margin 0
  .scroll
    width auto
    // animation scrollloop 6s linear infinite
    white-space nowrap
    // transition transform 0.03s linear
  .hidden
    display inline-block
    position absolute
    top 0
    left 0
    white-space nowrap
    visibility hidden
@keyframes scrollloop
  0%
    transform translate(0)
  100%
    transform translate(-645px)
</style>
