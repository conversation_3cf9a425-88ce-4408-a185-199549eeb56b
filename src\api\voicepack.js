import api from './api'
export default {
  // 语音红包开始
  go: postData => api.fetchBaseData('/pro/hxc/web/provoicepack/go.htm', postData),
  // 活动详情
  read: postData => api.fetchBaseData('/pro/hxc/web/provoicepack/read.htm', postData),
  // 上下轮切换
  switch: postData => api.fetchBaseData('/pro/hxc/web/provoicepack/switch.htm', postData),
  // 倒计时
  time: postData => api.fetchBaseData('/pro/hxc/web/provoicepack/time.htm', postData),
  // 重新启动
  restart: postData => api.fetchBaseData('/pro/hxc/web/provoicepack/restart.htm', postData),
  // 再来一轮
  again: postData => api.fetchBaseData('/pro/hxc/web/provoicepack/again.htm', postData),
  // 结束
  end: postData => api.fetchBaseData('/pro/hxc/web/provoicepack/end.htm', postData),
}
