import Vue from 'vue'
import vuex from '@/vuex/index'
import HiCheckActivityTime from './check-activity-time.vue'
import { PopupManager } from 'element-ui/lib/utils/popup'
import { Hi } from '@/libs/common'
HiCheckActivityTime.store = vuex

let CheckConstructor = Vue.extend(HiCheckActivityTime)

const CheckActivityTime = async () => {
  const wall = (vuex.state && vuex.state.wall && vuex.state.wall.wall) || {}
  if (Hi.String.getDate(wall.endDate).getTime() - new Date().getTime() > 0) {
    // 判断活动在有效期内，就不继续往后走了
    return Promise.resolve()
  }
  // 渲染活动结束提示框
  let zIndex = PopupManager.nextZIndex()
  let instance,
    fnPromise,
    options = {
      successfn: () => fnPromise && fnPromise.r(),
      failfn: () => fnPromise && fnPromise.j('fail'),
    }
  instance = new CheckConstructor({
    data: options,
  })
  instance.vm = instance.$mount()
  document.body.appendChild(instance.vm.$el)
  instance.vm.visible = true
  instance.dom = instance.vm.$el
  instance.dom.style.zIndex = zIndex
  instance.dom.id = 'hi_check_activity_time_' + zIndex
  await new Promise((r, j) => (fnPromise = { r, j }))
}

export default CheckActivityTime
