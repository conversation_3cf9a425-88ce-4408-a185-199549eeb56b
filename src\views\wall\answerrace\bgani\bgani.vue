<template>
  <div class="bgani-box flex flex-a-c flex-j-c">
    <hi-ani :jsonData="JSON.parse(jsonData)" class="ani flex flex-a-c flex-j-c"></hi-ani>
    <div class="shadow"></div>
  </div>
</template>

<script>
import jsonData from './data.json'
export default {
  name: 'bgani',
  data() {
    return {
      jsonData: JSON.stringify(jsonData)
        .replace(/images\//gi, '')
        .replace(/img_\d\.png/gi, function(s, s1) {
          return require(`./images/${s}`)
        }),
    }
  },
}
</script>

<style lang="stylus" scoped>
.bgani-box
  width 100%
  height 100%
  position absolute
  pointer-events none
.ani
  position absolute
  width 1280px
  height 800px
  height 100%
  top 0
  left 0
  right 0
  bottom 0
  margin auto
  >>> > div
    overflow visible !important
    transform none !important
.shadow
  position absolute
  bottom 0
  left 0
  width 100%
  height 292px
  background linear-gradient(to bottom, rgba(255, 255, 255, 0) 0%, rgba(0, 0, 0, 0.4) 70%, rgba(0, 0, 0, 0.5) 80%, rgba(0, 0, 0, 0.7) 90%, rgba(0, 0, 0, 1) 100%)
</style>
