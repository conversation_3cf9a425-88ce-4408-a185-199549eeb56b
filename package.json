{"name": "web-pcwall", "version": "3.2.254", "private": true, "scripts": {"dev": "set NODE_OPTIONS=--openssl-legacy-provider && npm run serve", "devpro": "vue-cli-service serve --mode devpro", "serve": "vue-cli-service serve --mode dev", "build": "vue-cli-service build", "build:test": "npm run build:uat", "build:uat": "vue-cli-service build --mode uat", "eslint": "eslint --ext .vue ./src", "eslint-fix": "eslint --fix --ext .vue ./src", "analyz": "cross-env npm_config_report=true npm run build"}, "dependencies": {"@vue/composition-api": "^1.7.0", "@vueuse/core": "^9.0.2", "aegis-web-sdk": "^1.24.45", "babel-polyfill": "^6.26.0", "cache-loader": "^4.1.0", "core-js": "^2.6.9", "devtools-detector": "^1.0.22", "echarts": "^4.9.0", "element-ui": "^2.14.1", "eslint": "^6.0.0", "eslint-plugin-vue": "^6.2.2", "gsap": "^3.7.0", "howler": "^2.2.3", "html2canvas": "^1.0.0-rc.5", "libpag": "^4.2.84", "lodash.throttle": "^4.1.1", "lz-string": "^1.4.4", "randomcolor": "^0.5.4", "svgaplayerweb": "^2.3.2", "vue": "^2.6.10", "vue-router": "^3.0.3", "vuex": "^3.0.1"}, "devDependencies": {"@babel/core": "^7.4.5", "@babel/preset-env": "^7.4.5", "@tweenjs/tween.js": "^17.4.0", "@vue/cli-plugin-babel": "^3.0.5", "@vue/cli-service": "^3.0.5", "@vue/runtime-dom": "^3.3.4", "animate.css": "^3.7.2", "axios": "^0.19.0", "babel-core": "^6.26.3", "babel-loader": "^8.0.5", "babel-plugin-component": "^1.1.1", "compression-webpack-plugin": "^3.0.0", "cross-env": "^6.0.3", "extract-text-webpack-plugin": "^4.0.0-beta.0", "html-webpack-plugin": "^3.2.0", "lodash.chunk": "^4.2.0", "lodash.debounce": "^4.0.8", "lodash.mergewith": "^4.6.1", "lottie-web": "^5.5.9", "mockjs": "^1.1.0", "normalize.css": "^8.0.1", "qrcode.vue": "^1.6.2", "stylus": "^0.54.5", "stylus-loader": "^3.0.2", "three": "^0.105.2", "vue-awesome": "^3.5.4", "vue-i18n": "8", "vue-seamless-scroll": "^1.1.23", "vue-template-compiler": "^2.6.10", "webpack-bundle-analyzer": "^3.6.0", "worker-loader": "^2.0.0"}}