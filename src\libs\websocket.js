import { env } from '@/libs/common'
const openDebug = !env.isPro

export default class HiWebsocket {
  // 要连接的URL
  url
  // WebSocket 实例
  ws
  // 上次成功连接时间，是否成功连接过
  lastSuccessTime = 0
  // 是否在重连中
  isReconnectionLoading = false
  // 延时重连的 id
  timeId = null
  // 是否是用户手动关闭连接
  isCustomClose = false
  // 错误消息队列
  errorStack = []
  // 消息回调
  messageCallback = null

  // 创建websocket
  constructor(url, messageCallback) {
    this.url = url
    this.messageCallback = messageCallback
    this.createWs()
  }
  async createWs() {
    if (this.ws) {
      this.ws.close()
      this.ws = null
      this.isCustomClose = true
    }
    openDebug && console.log('createWs')
    if ('WebSocket' in window) {
      this.ws = new WebSocket(this.url)

      try {
        await Promise.race([
          new Promise(resolve => (this.ws.onopen = () => resolve())),
          new Promise((r1, r2) => setTimeout(r2, 3000)),
        ])
        this.openEvent()
        this.isCustomClose = false
        this.onClose()
        this.onMessage()
      } catch (err) {
        this.createWs()
      }
    } else {
      openDebug && console.log('当前浏览器不支持websocket')
    }
  }
  openEvent() {
    openDebug && console.log('ws连接成功')
    this.lastSuccessTime = new Date().getTime()
    // 重发错误消息
    this.errorStack.forEach(item => this.send(item))
    this.errorStack = []
    this.isReconnectionLoading = false
    // 开启心跳
    this.heartBeat()
  }
  onClose() {
    this.ws.onclose = err => {
      openDebug && console.log('ws连接关闭', err)
      if (this.isCustomClose) return
      this.reconnection()
      this.isReconnectionLoading = false
    }
  }
  onMessage() {
    this.ws.onmessage = event => {
      const PONG_FLAG = '1'
      try {
        const data = JSON.parse(event.data)
        this.messageCallback && this.messageCallback(data)
      } catch (err) {
        if (event.data === PONG_FLAG) return
        this.messageCallback && this.messageCallback(event.data)
      }
    }
  }
  heartBeat() {
    setTimeout(() => {
      const heartFlag = 0
      if (this.ws && this.ws.readyState === 1) {
        this.send(heartFlag)
        this.heartBeat()
      }
    }, 2000)
  }
  reconnection() {
    openDebug && console.log('ws重连中')
    if (this.isReconnectionLoading || this.isCustomClose) return
    this.isReconnectionLoading = true
    clearTimeout(this.timeId)
    this.timeId = setTimeout(() => {
      this.createWs()
    }, 2000)
  }
  send(data) {
    if (this.ws.readyState !== 1) {
      this.errorStack.push(data)
      if (this.lastSuccessTime > 0) {
        this.reconnection()
      }
      return
    }
    this.ws.send(JSON.stringify(data))
  }
  close() {
    this.isCustomClose = true
    this.ws.close()
  }
  start() {
    this.isCustomClose = false
    this.createWs()
  }
  getWs() {
    return this.ws
  }
  clearErrorStack() {
    this.errorStack = []
  }
}
