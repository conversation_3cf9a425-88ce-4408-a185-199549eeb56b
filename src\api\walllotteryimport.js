import api from './api'

export default {
  // 查询可以中奖的粉丝列表
  lotteryList: postData => api.fetchBaseData('/pro/hxc/web/prowalllotteryimport/lotteryList.htm', postData),
  // 删除中奖者信息
  recordDelete: postData => api.fetchBaseData('/pro/hxc/web/prowalllotteryimport/recordDelete.htm', postData),
  // 查询已经中奖的名单列表
  recordList: postData => api.fetchBaseData('/pro/hxc/web/prowalllotteryimport/recordList.htm', postData),
  // 保存中奖人列表
  report: postData => api.fetchBaseData('/pro/hxc/web/prowalllotteryimport/report.htm', postData),
  // 内定 接口
  nd: postData => api.fetchBaseData('/pro/hxc/web/prowalllotteryimport/nd.htm', postData),
  //
  importList: postData => api.fetchBaseData('/pro/hxc/web/prowalllotteryimport/importList.htm', postData),
}
