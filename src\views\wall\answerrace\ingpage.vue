<template>
  <div class="ing-page" v-if="readyDone">
    <!-- 顶部内容区域 -->
    <div class="ing-header flex flex-a-c flex-j-a">
      <template>
        <template v-if="!isScore">
          <span v-if="awardSwitch && prizeInfo.type == 'REDPACK'" class="prize-total">本轮奖池{{ prizeInfo.redpackAmout | fenToYuan }}元</span>
          <span v-else-if="awardSwitch" class="prize-total limit">本轮奖品: {{ prizeInfo.name }}</span>
        </template>
        <span class="people-participant">{{ participant }}人参与</span>
        <span class="right-people">{{ rightText }}</span>
      </template>
    </div>
    <!-- 3，2，1，倒计时 -->
    <hi-countdown v-if="sec > 0 && !index" :config="{ sec }" @over="countDownObj.resolve"></hi-countdown>
    <!-- 答题的主题 -->
    <div v-else-if="showQId" class="answer-body flex flex-j-sb">
      <rank-list class="rank" :rank-list="rankArr"></rank-list>
      <div class="right-cont">
        <div class="rounds">{{ index }} / {{ total }}</div>
        <div class="tit flex">
          <div :style="titStyle">
            <p class="type">（{{ resultType }}）</p>
            {{ question.title }}
          </div>
          <img v-if="question.img" class="tit-img" :src="question.img" />
        </div>
        <div v-if="!isShowComments" class="answers-box flex flex-w-w">
          <div v-for="item in optionArr" :key="item.id" :style="itemboxStyle()" class="flex flex-a-c flex-j-c">
            <div class="answer-item" :class="itemClass(item)">
              <img v-if="question.type !== 'TXT' && item.img" :src="item.img" />
              <div class="text">
                <div v-if="ingState === 'done'" class="p-box" :style="pBoxStyle(item)"></div>
                <div class="text-cont flex">
                  <span class="t">{{ item.index }}. </span>
                  <p>{{ item.title }}</p>
                  <span v-if="ingState === 'done'" class="percent">{{ percent(item.id) }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div v-else class="comments-box">
          <h4>注释</h4>
          <p>本题正确选项为{{ rightResultText }}。</p>
          <p class="cont">{{ question.remark }}</p>
        </div>
        <div v-if="ingState === 'done'" class="btn-group flex flex-a-c flex-j-c">
          <div class="over-btn" @click="over">结束
            <span v-if="!isShowNext && wallanswerrace.autoSwitch === 'Y'">（{{lastTime}}）</span>
          </div>
          <div v-if="isShowNext" class="next-btn" @click="switchAnswer('right')">下一题<span v-if="wallanswerrace.autoSwitch === 'Y'">（{{lastTime}}）</span></div>
        </div>
      </div>
      <!-- 倒计时 -->
      <div class="countdown-box" v-if="ingState === 'timer'">
        <hi-countdown1 :style="{ zIndex: 999 }" :fontSize="48" ref="countDown" color="rgba(255,255,255,0.9)" :config="{ start: questionCountDownTime }" @over="questionCountDownObj.resolve && questionCountDownObj.resolve()"></hi-countdown1>
      </div>
    </div>
    <!-- 进入下一题之前的提示 -->
    <div class="tips-box" v-if="ingState === 'loading' || nextLoading">
      <span>{{ ingState === 'loading' ? '正在统计结果' : '即将进入下一题' }}</span>
    </div>
  </div>
</template>
<script>
import { timer } from '@/libs/common'
import { mapGetters, mapActions, mapMutations } from 'vuex'
import { relationUserMixin, wallMixin } from '@/libs/mixins'
import api from '@/api'
import HiCountdown from '@/views/common/countdown'
import HiCountdown1 from '@/views/common/countdown1'
import RankList from './ranklist'
export default {
  name: 'ingpage',
  isFull: true,
  mixins: [relationUserMixin, wallMixin],
  components: {
    HiCountdown,
    HiCountdown1,
    RankList,
  },
  props: {
    themeConfig: Object,
  },
  data() {
    return {
      readyDone: false,
      index: 0,
      rankArr: [],
      countDownObj: {},
      sec: 0,
      questionCountDownTime: null, // 答题倒计时
      questionCountDownObj: {},
      survivalPeople: 0,
      rightPeople: 0, // 答对的人
      participant: 0,
      record: null,
      isShowComments: false,
      nextLoading: false,
      showQId: 0, // 当前展示的问题id
      lastTime: 30,
    }
  },
  computed: {
    ...mapGetters({
      wallanswerrace: 'answerrace/getWallanswerrace',
      questionId: 'answerrace/questionId',
      prizeInfo: 'answerrace/prizeInfo',
      questionObj: 'answerrace/questionObj',
      ingState: 'answerrace/getIngState',
    }),
    answerraceId() {
      return this.wallanswerrace.id
    },
    isScore() {
      return this.wallanswerrace.gameMode === 'score' // true 积分模式
    },
    awardSwitch() {
      return this.wallanswerrace.awardSwitch === 'Y'
    },
    // 总题数
    total() {
      return Object.keys(this.questionObj).length
    },
    // 当前显示的问题对象
    question() {
      return this.questionObj[this.showQId].question
    },
    // 当前问题选项
    optionArr() {
      return this.questionObj[this.showQId].optionArr
    },
    resultType() {
      const arr = this.questionObj[this.showQId].result
      return arr.length > 1 ? '多选题' : '单选题'
    },
    // 当前问题答案
    rightResultText() {
      const arr = this.questionObj[this.questionId].result
      let str = ''
      arr.forEach((item, index) => {
        if (index) {
          str += `、${item.index}`
        } else {
          str += item.index
        }
      })
      return str
    },
    // 是否显示下一题按钮
    isShowNext() {
      if (this.index === this.total) return false
      if (!this.isScore && !this.survivalPeople) return false
      return true
    },
    titStyle() {
      let obj = {
        width: this.question.img ? '420px' : '748px',
        textAlign: this.question.img ? 'left' : 'center',
      }
      if (!this.question.img) {
        obj.margin = '0 auto'
      }
      return obj
    },
    rightText() {
      return this.isScore ? `${this.rightPeople}人正确` : `${this.survivalPeople}人通关`
    },
  },
  watch: {
    questionId(v, o) {
      if (v !== o && v) {
        this.resetData()
        this.init.call(this)
      }
    },
    ingState: {
      immediate: true,
      handler(v) {
        if (v === 'done') {
          this.changeLastTime()
        }
      }
    }
  },
  methods: {
    ...mapActions({
      updateWallanswerrace: 'answerrace/updateWallanswerrace',
      calcActivityTime: 'common/calcActivityTime',
    }),
    ...mapMutations('answerrace', ['setIngState']),
    itemClass(item) {
      return {
        tuwen: this.question.type === 'IMG' || this.question.type === 'IMG_TXT',
        wen: !this.question.type || this.question.type === 'TXT',
        right: item.rightAnswer === 'Y' && this.ingState === 'done',
      }
    },
    itemboxStyle() {
      const len = this.optionArr.length
      if (!len) return {}
      let w = '50%'
      if (len > 4) w = '33.33%'
      return {
        width: w,
        height: '212px',
      }
    },
    rate(id) {
      if (!this.record) return '0%'
      const rate = this.record[id] ? (this.record[id] / this.participant) * 100 : 0
      return parseInt(rate) + '%'
    },
    percent(id) {
      // 按人数
      if (this.wallanswerrace.optionShowType === 'NUMBER') return this.record[id] || 0
      // 按比例
      return this.rate(id)
    },
    pBoxStyle(item) {
      let obj = { width: this.rate(item.id) }
      if (item.rightAnswer === 'Y') {
        obj.backgroundColor = '#ff6600'
      }
      return obj
    },
    // 查询参与人数
    async queryPartant() {
      try {
        let data = await api.answerraceregedit.count({
          where: {
            wallFlag: this.wallFlag,
            answerraceId: this.wallanswerraceId,
          },
        })
        if (data) {
          this.participant = data.map.count || 0
        }
      } catch (error) {
        console.log(error)
      }
    },
    resetData() {
      this.readyDone = false
      this.rightPeople = 0
      this.countDownObj = {}
      this.questionCountDownTime = null
      this.questionCountDownObj = {}
    },
    async init() {
      this.readyDone = true
      if (!this.questionObj[this.questionId]) return
      // 根据时间差判断
      const time = this.questionObj[this.questionId].question.startDate
      let offset = await this.calcActivityTime(time)
      console.log('offset', offset)
      if (offset < 0) {
        const time = Math.abs(offset)
        if (!this.index) {
          // 从开始按钮进来
          // 321倒计时
          this.sec = time
          await new Promise((resolve, reject) => (this.countDownObj = { resolve, reject }))
          this.sec = 0
        } else {
          this.nextLoading = true
          await timer(time * 1000)
          this.nextLoading = false
        }
        offset = 0
      }
      this.showQId = this.questionId
      // 题目倒计时开始  --- 1.显示题目
      const countDown = this.wallanswerrace.countDown || 10
      this.index = this.questionObj[this.questionId].questionIndex
      if (offset >= 0 && offset < countDown) {
        this.questionCountDownTime = countDown - offset
        this.setIngState('timer')
        await new Promise((resolve, reject) => (this.questionCountDownObj = { resolve, reject }))
        offset = countDown
      }
      // 题目倒计时结束 ---  显示结果  占比什么的
      if (offset >= countDown) {
        this.setIngState('loading')
        await timer(1000)
        await this.fetchResult()
        this.setIngState('done')
      }
    },
    // 获取结果
    async fetchResult() {
      //查通关人数或正确人数  并显示
      for (let i = 0; i < 5; i++) {
        try {
          const where = { answerraceId: this.answerraceId, questionId: this.questionId }
          let arr = [
            api.answerracerecord.list({ where }), // 选择各个选项的多少人
            api.answerracerecord.ranking({ where, size: 10 }),
          ]
          if (this.isScore) {
            await this.queryPartant() // 积分模式需要及时更新参与人数
            arr.push(api.answerracerecord.rightOptionCnt({ where })) // 积分模式 查正确的人
          } else {
            arr.push(api.answerracewin.count({ where })) // 闯关模式 通关的人
          }
          const [record, r, data] = await Promise.all(arr)
          this.record = record
          this.rankArr = r.ranking || []
          this.isScore ? (this.rightPeople = data || 0) : (this.survivalPeople = data.count || 0)
          break
        } catch (error) {
          await timer(500)
        }
      }
    },
    // 结束
    async over() {
      if (this._isEnding) return
      try {
        this._isEnding = true
        await api.answerrace.endanswerrace({ where: { wallFlag: this.wallFlag } })
      } catch (error) {
        console.log(error)
      }
      this._isEnding = false
    },
    // 切换下一题
    async switchAnswer(dir = 'right') {
      console.log(dir)
      if (dir === 'left') {
        if (this.ingState === 'done') this.over()
        return
      }
      // 只有结果显示的页面才能触发
      // 防止 频繁 触发 下一题 并显示 tips
      if (this.ingState !== 'done' || this._switchFlag) return
      this._switchFlag = true
      // 调接口
      try {
        await api.answerracequestion.switch({
          where: {
            wallFlag: this.wallFlag,
          },
        })
        this.isShowComments = false
      } catch (error) {
        error.msg && this.$tip.msg(error.msg)
        console.log(error)
      }
      this._switchFlag = false
    },
    async toggleComment() {
      if (this.ingState !== 'done' || this._flag) return
      this._flag = true
      this.isShowComments = !this.isShowComments
      await timer(1000)
      this._flag = false
    },
    async changeLastTime() {
      if (this.wallanswerrace.autoSwitch === 'Y') {
        this.lastTime = this.wallanswerrace.autoSwitchtime
        this.autoSwitchinterval && clearInterval(this.autoSwitchinterval)
        this.autoSwitchinterval = setInterval(async () => {
          if (this.lastTime > 0) {
            this.lastTime--
          } else {
            await this.switchAnswer('right')
            clearInterval(this.autoSwitchinterval)
          }
        }, 1000);
      }
    }
  },
  async mounted() {
    // 查下参与人数
    await this.queryPartant()
    if (this.questionId) {
      this.init()
    }
    // 结束  下一题  注释
    this.handler = {
      'control-left-right': this.switchAnswer.bind(this),
      'keyup-Space': this.toggleComment.bind(this), //
    }
    // 绑定快捷键
    Object.keys(this.handler).forEach(key => this.$bus.$on(key, this.handler[key]))
  },
  destroyed() {
    clearInterval(this.autoSwitchinterval)
    this.setIngState('')
    this.rankArr = []
    this.survivalPeople = 0
    this.participant = 0
    // 取消快捷键
    Object.keys(this.handler).forEach(key => this.$bus.$off(key, this.handler[key]))
  },
}
</script>

<style lang="stylus" scoped>
@import "~@/assets/css/stylus.styl"
// ing页面
.ing-page
  size(100% 100%)
  relative(t 0, l 0)
  overflow hidden
.ing-header
  size(1240px 65px)
  margin 0 auto
  color #fff
  font-size 24px
  >span
    padding-left 40px
    margin-right 20px
  .right-people
    background url("./images/right-s.png") no-repeat
    background-position 23px center
    color #00f5b8
    border-left 3px solid #fff
    padding-left 60px
  .prize-total
    max-width 200px
    overflow hidden
    white-space nowrap
    text-overflow ellipsis
    background url("./images/prizepoll.png") no-repeat
    background-position 0 center
  .people-participant
    background url("./images/participant.png") no-repeat
    background-position 0 center
.answer-body
  relative(t 0, l 0)
  size(1240px 656px)
  margin 0 auto
  line-height 1.5
.right-cont
  padding 16px
  width 978px
  height 100%
  margin-left 14px
  border-radius 8px
  color #fff
  background-color rgba(0, 0, 0, 0.6)
  box-sizing border-box
  .rounds
    width 100%
    text-align center
    font-size 40px
    margin-top -76px
  .tit
    margin-top 16px
    padding-left 16px
    height 160px
    line-height 1.8
    font-size 20px
    font-family "Adobe 黑体 Std"
    transition width 0.3s ease-in
    .type
      margin 0
      text-align center
  .tit-img
    margin-left 82px
    width 322px
    height 150px
    object-fit contain
    border-radius 5px
.answer-zone
  width 100%
  height 468px
  margin 0 auto
.answers-box
  size(100% 425px)
.answer-item
  position relative
  width 280px
  border-radius 5px
  overflow hidden
  box-sizing border-box
  border 3px solid transparent
  .qimg
    width 100%
    height 100%
  .text
    position relative
    padding 5px 12px
    box-sizing border-box
    .text-cont
      position relative
      z-index 1
      width 100%
      height 100%
      p
        margin 0
        padding-right 30px
      .percent
        width 56px
        text-align right
      .t
        height 38px
    .p-box
      absolute(t 0, l 0)
      z-index 0
      width 50px
      height 100%
      background-color #ffae00
      transition all 0.3s linear
.answer-item.right
  border 3px solid #00f5b8
  &::after
    content ""
    absolute(r 6px, t 6px)
    width 36px
    height 36px
    background url("./images/right.png") no-repeat
.tuwen
  height 174px
  background-color rgba(0, 0, 0, 0.8)
  font-size 16px
  line-height 1.1
  img
    width 100%
    height 130px
    border-radius 5px 5px 0 0
  .text
    height 44px
    p
      width 217px
      word-break break-all
      display -webkit-box
      overflow hidden
      text-overflow ellipsis
      -webkit-line-clamp 2
      -webkit-box-orient vertical
    .percent
      line-height 34px
.wen
  height 94px
  background-color #6e65e7
  .text
    height 100%
    .text-cont
      padding 6px 0
      font-size 16px
      line-height 26px
      .percent
        absolute(r 0, b 5px)
        font-size 22px
      .t
        height 58px
      p
        display -webkit-box
        overflow hidden
        text-overflow ellipsis
        -webkit-line-clamp 3
        -webkit-box-orient vertical
.btn
  margin 0 10px
  width 120px
  height 38px
  cursor pointer
.comments-box
  padding 0 30px
  height 425px
  overflow hidden
  font-size 20px
  animation showSlow 0.8s ease 0.6s backwards
  font-family "heiti"
  color rgba(255, 255, 255, 0.9)
  h4
    padding-top 18px
    margin-bottom 0
    width 190px
    font-size 32px
    border-top 2px dashed
  .cont
    line-height 1.7
    margin-top 32px
    width 90%
    height 200px
    overflow hidden
.rank
  flex-shrink 0
.countdown-box
  absolute(t -55px, r 0)
  size(115px 115px)
  z-index 99
  background-color rgba(0, 0, 0, 0.4)
  border-radius 50%
.tips-box
  absolute(t 384px, l 50%)
  z-index 9999
  size(240px 144px)
  margin -72px 0 0 -120px
  border-radius 10px
  font-size 16px
  color #fff
  background rgba(0, 0, 0, 0.8)
  flex()
.next-btn
  width 120px
  height 38px
  text-align center
  line-height 38px
  margin 0 20px
  background url("./images/btn-next.png") no-repeat
.over-btn
  width 120px
  height 38px
  margin 0 20px
  text-align center
  line-height 38px
  background url("./images/btn-over.png") no-repeat
</style>
