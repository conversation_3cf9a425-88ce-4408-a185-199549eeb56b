import api from './api'

export default {
  login: postData => api.fetchBaseData('/pro/hxc/prouser/login.htm', postData),
  usershortcutList: postData => api.fetchBaseData('/pro/hxc/web/prousershortcut/list.htm', postData),
  wxauthconfigread: postData => api.fetchBaseData('/pro/hxc/wxauthconfig/read.htm', postData),
  independent: postData => api.fetchBaseData('/pro/hxc/web/prouserindependent/independent.htm', postData),
}
