<template>
  <div class="wrap">
    <canvas ref="canvas"></canvas>
  </div>
</template>
<script>
function Particle (xPoint, yPoint) {
  this.w = this.h = Math.random() * 4 + 1
  this.x = xPoint - this.w / 2
  this.y = yPoint - this.h / 2
  this.vx = (Math.random() - 0.5) * 10
  this.vy = (Math.random() - 0.5) * 10
  this.alpha = Math.random() * 0.2 + 0.8
  this.color
}

Particle.prototype = {
  gravity: 0.05,
  move: function () {
    this.x += this.vx
    this.vy += this.gravity
    this.y += this.vy
    this.alpha -= 0.01
    if (this.x <= -this.w || this.x >= screen.width || this.y >= screen.height || this.alpha <= 0) {
      return false
    }
    return true
  },
  draw: function (c) {
    c.save()
    c.beginPath()
    c.translate(this.x + this.w / 2, this.y + this.h / 2)
    c.arc(0, 0, this.w, 0, Math.PI * 2)
    c.fillStyle = this.color
    c.globalAlpha = this.alpha
    c.closePath()
    c.fill()
    c.restore()
  },
}
import { Hi } from '@/libs/common'
export default {
  name: 'fireworks',
  data () {
    return {
      canvas: null,
      ctx: null,
      w: null,
      h: null,
      particles: [],
      probability: 0.04,
      xPoint: null,
      yPoint: null,
    }
  },

  methods: {
    resizeCanvas () {
      if (!!this.canvas) {
        this.w = this.canvas.width = window.innerWidth
        this.h = this.canvas.height = window.innerHeight
      }
    },
    update () {
      if (this.particles.length < 1000 && Math.random() < this.probability) {
        this.createFirework()
      }
      var alive = []
      for (var i = 0; i < this.particles.length; i++) {
        if (this.particles[i].move()) {
          alive.push(this.particles[i])
        }
      }
      this.particles = alive
    },
    paint () {
      this.ctx.clearRect(0, 0, this.w, this.h)
      this.ctx.globalCompositeOperation = 'source-over'
      this.ctx.fillStyle = 'rgba(0,0,0,0)'
      this.ctx.fillRect(0, 0, this.w, this.h)
      this.ctx.globalCompositeOperation = 'lighter'
      for (var i = 0; i < this.particles.length; i++) {
        this.particles[i].draw(this.ctx)
      }
    },
    createFirework () {
      this.xPoint = Math.random() * (this.w - 200) + 100
      this.yPoint = Math.random() * (this.h - 200) + 100
      var nFire = Math.random() * 50 + 100
      var c =
        'rgb(' +
        ~~(Math.random() * 200 + 55) +
        ',' +
        ~~(Math.random() * 200 + 55) +
        ',' +
        ~~(Math.random() * 200 + 55) +
        ')'
      for (var i = 0; i < nFire; i++) {
        var particle = new Particle(this.xPoint, this.yPoint)
        particle.color = c
        var vy = Math.sqrt(25 - particle.vx * particle.vx)
        if (Math.abs(particle.vy) > vy) {
          particle.vy = particle.vy > 0 ? vy : -vy
        }
        this.particles.push(particle)
      }
    },
  },
  mounted () {
    this.canvas = this.$refs.canvas
    this.ctx = this.canvas.getContext('2d')
    this.resizeCanvas()
    this.renderStop = Hi.AnimateLoop.setup(v => {
      this.update()
      this.paint()
    })
  },
  destroyed () {
    this.renderStop()
  },
}
</script>

<style lang="stylus" scoped>
.wrap
  width 100%
  height 100%
  position absolute
  left 0
  top 0
  z-index 9

  canvas
    width 100%
    height 100%
</style>
