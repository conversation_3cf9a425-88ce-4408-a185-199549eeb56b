import api from './api'

export default {
  // 活动信息
  read: postData => api.fetchBaseData('/pro/hxc/web/prowallvotesubject/read.htm', postData),

  // 投票数总数
  data: postData => api.fetchBaseData('/pro/hxc/web/prowallvotesubject/data.htm', postData),

  // 上下轮切换
  switch: postData => api.fetchBaseData('/pro/hxc/web/prowallvotesubject/switch.htm', postData),

  // 投票的开始和暂停
  stop: postData => api.fetchBaseData('/pro/hxc/web/prowallvotesubject/stop.htm', postData),

  // 投票参与人数
  count: postData => api.fetchBaseData('/pro/hxc/web/prowallvotesubject/count.htm', postData),

  // 排行榜
  ranking: postData => api.fetchBaseData('/pro/hxc/web/prowallvotesubject/ranking.htm', postData),
  // 投票项
  suboptionpage: postData => api.fetchBaseData('/pro/hxc/web/prowallvotesuboption/page.htm', postData),
}
