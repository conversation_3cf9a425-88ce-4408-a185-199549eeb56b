<template>
  <div class="contract-box positionall" v-if="wallcontract && wallcontract.id" ref="imageWrapper">
    <img class="bg positionall" crossorigin="anonymous"
      :src="`${wallcontract.screenImg || 'https://res3.hixianchang.com/qn/contract/contarct-default.png'}`" />
    <!-- 内容区域 -->
    <div class="content positionall">
      <!-- 标题 -->
      <!-- 真正的动画区域 -->
      <div class="ani-zone" ref="aniZone">
        <!-- canvas 图在canvas 上画   然后整个canvas  缩放  最后导出图片   只执行一次 -->
        <!-- 这里应该是一个循环 是N个签名对应的canvas图形 默认显示大的 等需要播放动画的时候动态修改 当前canvas 的尺寸和位置 -->
        <canvas class="cell" v-for="(item, index) in signaturePositionList" :key="index" :data-id="item.id"
          :ref="`canvas_${item.deviceId}`" :style="canvasPos(item)" :width="parseInt(canvasPos(item).width)"
          :height="parseInt(canvasPos(item).height)">
        </canvas>

        <!-- 备用签名点击位置 -->
        <div class="cell stand" v-for="item in signaturePositionList" :key="item.id" :style="standbyPos(item)"
          @click="playStandby($event, item)" @dblclick="playAllStandby"></div>
      </div>
      <!-- 弹窗 -->
      <transition name=" fade">
        <div class="tips" v-if="isShowTips">{{ tipsCont }}</div>
      </transition>
    </div>
    <hi-fireworks v-if="showFireWork"></hi-fireworks>
  </div>
  <hi-noact v-else type="签约墙" modelName="contract"></hi-noact>
</template>

<script>
// 1: 'SAME_GESTURE',//实时笔画
// 2: 'SAME_MANUAL_SYNC',//手动提交 实时播放
// 3: 'SAME_MANUAL_SAMETIME', //手动提交 同时播放
// 4: 'DIFFERENT_GESTURE_CONTINUOUS',//实时比划 依次落款
// 5: 'DIFFERENT_GESTURE_SAMETIME',//实时比划 同时落款
// 6: 'DIFFERENT_MANUAL_CONTINUOUS', //手动提交 依次落款
// 7: 'DIFFERENT_MANUAL_SAMETIME',//手动提交 同时落款
let flyList = ['DIFFERENT_GESTURE_CONTINUOUS', 'DIFFERENT_MANUAL_CONTINUOUS']
let noFlyList = ['SAME_GESTURE', 'SAME_MANUAL_SYNC', 'SAME_MANUAL_SAMETIME']
let togetherFlyList = ['DIFFERENT_GESTURE_SAMETIME', 'DIFFERENT_MANUAL_SAMETIME']
import { mapGetters, mapActions } from 'vuex'
import HiNoact from '../noact'
import api from '@/api'
import { Hi, timer, debounce } from '@/libs/common'
import html2canvas from 'html2canvas'
import TWEEN from '@tweenjs/tween.js'
import HiFireworks from './components/fireworks'
import createFlower from './components/flower'
import SuperGif from './libgif.js'
import throttle from 'lodash.throttle'
const { Tween } = TWEEN
export default {
  name: 'contract',
  components: { HiNoact, HiFireworks },
  data() {
    return {
      isReady: false, //准备完毕
      syncWriting: false, //是否在同步实时签名数据
      showFireWork: false,
      loading: false,
      scale: 1,
      completeDraw: {}, //已经完成签名笔画绘制
      completeFly: {}, //完成绘制的签名和飞入,存设备id
      signaturePositionList: [], //签名的位置信息数据
      signDevicePosIdObj: {}, //签名设备对应的位置id数据对象
      isShowTips: false, // 是否显示弹窗
      oT_Tips: null, // 控制弹窗的计时器
      saveState: 'isSaved', // 控制弹窗中显示的文本
      signatureList: [], //非实时签名数据或者实时签名完成后的数据
      //needDrawSignList: [], //需要绘制的签名数据
      flySignList: [], //需要统一飞的签名
      deviceList: [], //签名设备数据list
      deviceObj: {}, //签名设备数据对象
      drawIng: {}, //正在绘制的设备签名
      drawInter: {}, //用来存绘制的定时器
      showPicStandby: {}, //显示图片备用签名的deviceId对象
      throttlePlay: throttle(() => this.playSign(), 15, {
        trailing: true,
        leading: true,
      }),
      flyPromiseRejectObj: {}, //飞入签名的promise.reject对象,用于清空数据时取消飞入
    }
  },
  computed: {
    ...mapGetters({
      wallcontract: 'contract/getWallcontract',
      resultWidth: 'common/resultWidth',
      windowWidth: 'common/windowWidth',
      windowHeight: 'common/windowHeight',
    }),
    // 弹窗文字
    tipsCont() {
      // 取决于是否已经进行了图片保存
      if (this.saveState === 'isSaved') {
        return '请勿重复保存'
      }
      if (this.saveState === 'isSaving') {
        return '正在保存，请稍后！'
      }
      if (this.saveState === 'isSaveDone') {
        return '保存成功'
      }
      return '保存成功'
    },
    // {"x0":-208,"y0":268,"x1":-100,"y1":-20,"width":540}
    // canvas 样式
    canvasPos() {
      return pos => {
        let w = this.canvasRect.width_big,
          h = this.canvasRect.height_big
        return {
          top: pos.y0 + 'px',
          left: pos.x0 + 'px',
          width: w + 'px',
          height: h + 'px',
        }
      }
    },
    canvasRect() {
      let ratio = 9 / 16,
        bigW = this.wallcontract.screensignatureSize1 * this.scale,
        smallW = this.wallcontract.screensignatureSize2 * this.scale
      return {
        width_big: bigW,
        height_big: bigW * ratio,
        width_small: smallW,
        height_small: smallW * ratio,
      }
    },
    //备用签名的位置和style,即正常签名的最终位置和style
    standbyPos() {
      return pos => {
        if (this.wallcontract.contractType === 'DIFFERENT_LOCATION') {
          //落款和签名在不同位置的时候
          return {
            top: pos.y1 + 'px',
            left: pos.x1 + 'px',
            width: this.canvasRect.width_small + 'px',
            height: this.canvasRect.height_small + 'px',
          }
        } else {
          //落款和签名同位置，(x0,y0)即为签名位置
          return {
            top: pos.y0 + 'px',
            left: pos.x0 + 'px',
            width: this.canvasRect.width_big + 'px',
            height: this.canvasRect.height_big + 'px',
          }
        }
      }
    },
    //播放速度
    speed() {
      return Number(1 / this.wallcontract.speed) || 1
    },
    //实时签名
    isOnLineSign() {
      let arr = ['SAME_GESTURE', 'DIFFERENT_GESTURE_CONTINUOUS', 'DIFFERENT_GESTURE_SAMETIME']
      return arr.includes(this.wallcontract.contractAnimation)
    },
    //签名设备总量
    deviceNumber() {
      return this.deviceList.length
    },
  },
  watch: {
    // 监听轮次信息的变化
    wallcontract: {
      immediate: true,
      handler(v, o) {
        // 这里只处理 切换轮次的情况
        if (v && o && v.id !== o.id) {
          this.init()
        }
      },
    },
    isShowTips(v, o) {
      if (v && !o) {
        if (this.oT_Tips) {
          return
        }
        this.oT_Tips = setTimeout(() => {
          this.isShowTips = false
          this.oT_Tips = null
        }, 2000)
      }
    },
  },
  methods: {
    reset() {
      if (this.wallcontract && this.wallcontract.id) {
        this.signaturePositionList.forEach(i => clearInterval(this.drawInter[i.id]))
        this.signaturePositionList = []
        this.signDevicePosIdObj = {}
        this.needDrawSignList = []
        this.flySignList = []
        this.isShowTips = false
        clearInterval(this.oT_Tips)
        this.oT_Tips = null
        this.saveState = 'isSaved'
        this.completeDraw = {}
        this.completeFly = {}
        this.drawIng = {}
        this.showPicStandby = {}
        this.showFireWork = false
        this.signatureList = []
        Object.keys(this.flyPromiseRejectObj).forEach(key => {
          this.flyPromiseRejectObj[key].forEach(fn => fn())
        })
      }
    },
    // 初始化方法 这里做准备工作 比如：  1、 初始化 canvas 的位置  2、 清空签名数据  3、
    async init() {
      try {
        this.isReady = false
        // 开始之前先重置
        this.reset()
        // 先处理图像信息
        await this.dealBgImgInfo()
        // 第一步 首先要拿到后台设计的签名的位置数组 然后 渲染出来canvas 节点  等待 画图
        await this.dealPostion()
        //查询设备信息
        await this.queryDevicecontract()
        await this.loadStandySign()
        // 等待渲染出页面
        await this.$nextTick()
        await this.querySignatureInfo()
        this.isReady = true
      } catch (err) {
        console.log(err)
        this.$tip.msg(err.msg || err.message || '网络异常！', 2000)
      }
    },
    async dealBgImgInfo() {
      try {
        // 加载背景图
        let bgImg = await Hi.Load.img(this.wallcontract.screenImg)
        let scale = 1 // 这个值是用来计算canvas 签名位置 相对于 图片左上角的位置
        // 换句话说 就是 图片相对于原始大小 缩放的倍数
        let bgw = bgImg.width,
          bgh = bgImg.height
        let parentDom = this.$refs.aniZone
        let imgR = bgw / bgh
        // 计算比例  确定高度 然后 在确定需要显示的宽度
        let realH, realW // 都是来控制盒子大小 位置的
        //  第一种情况就是 高度显示完   宽度没显示完
        if (this.windowWidth / this.windowHeight > imgR) {
          realH = 800
          realW = 800 * imgR
          scale = 800 / bgh
        } else {
          realH = this.resultWidth / imgR
          realW = this.resultWidth
          scale = this.resultWidth / bgw
        }
        parentDom.style.width = realW + 'px'
        parentDom.style.height = realH + 'px'
        parentDom.style.backgroundImage = `url(${this.wallcontract.screenImg})`
        parentDom.style.backgroundSize = '100% 100%'
        this.scale = scale
      } catch (e) {
        console.log(e)
      }
    },
    //实时笔画签约
    async doSyncWritingGo(data) {
      if (!this.isReady) return
      await this.dealSyncWritingData(data)
      this.throttlePlay()
    },
    //处理实时笔画同步数据
    async dealSyncWritingData(data) {
      try {
        //如果所有设备都确定，返回
        if (this.signatureList.length === this.deviceNumber) {
          return
        }
        let { contractId, deviceId, stepPoints, clear, strokeIndex, width } = data
        //如果数据不是当前轮次，返回
        if (contractId !== this.wallcontract.id) return
        let index = this.needDrawSignList.findIndex(i => i.deviceId === deviceId)
        let queryIndex = this.signatureList.findIndex(i => i.deviceId === deviceId)
        if (queryIndex > -1) return //如果已经确定签名，则不要绘制了

        //处理数据，进行缩放计算
        let scale = this.canvasRect.width_big / width || 1
        stepPoints.map(item => {
          item[0] = Math.floor(item[0] * scale)
          item[1] = Math.floor(item[1] * scale)
          return item
        })

        if (index > -1) {
          // //如果没有数据
          if (this.needDrawSignList[index].spareSignatureType) {
            //如果已经启用了备用签名，就不需要绘制了
            return
          }
          let needDrawSign = this.needDrawSignList[index]
          let tempData = needDrawSign.signaturePosition

          if (clear) {
            //清除，则stepPoints数据清空
            tempData = {
              width,
              stepPoints: [],
            }
          } else {
            //不清除，根据笔画是否更新塞入数据
            if (needDrawSign.strokeIndex == strokeIndex) {
              let l = tempData.stepPoints.length
              //如果没有开始新的笔画
              tempData.stepPoints[l - 1].push(...stepPoints)
            } else {
              tempData.stepPoints.push(stepPoints)
            }
          }
          needDrawSign.strokeIndex = strokeIndex
          needDrawSign.signaturePosition = tempData
        } else {
          this.needDrawSignList.push({
            type: 'sameTime',
            deviceId,
            signaturePosition: { width, stepPoints: [stepPoints] },
            strokeIndex,
          })
        }
      } catch (err) {
        console.log(err)
      }
    },
    // 查询签名数据
    async querySignatureInfo(from) {
      try {
        // 查询签名信息
        let data = await api.contractsignature.list({
          where: {
            contarctId: this.wallcontract.id,
          },
        })
        this.signatureList = data
        //如果实时签名，定格后，需要将数据替换城定格数据
        if (this.isOnLineSign) {
          data.forEach(item => {
            //将实时签名数据替换定格签名数据
            let index = this.needDrawSignList.findIndex(i => i.deviceId === item.deviceId)
            if (index > -1) {
              this.needDrawSignList.splice(index, 1, item)
            } else {
              this.needDrawSignList.push(item)
            }
          })
        } else {
          //非实时签名，数据本就来源于这个接口
          let obj = {}
          this.needDrawSignList.forEach(i => (obj[i.deviceId] = i))
          data.forEach(item => {
            if (!obj[item.deviceId]) {
              this.needDrawSignList.push(item)
            }
          })
        }
        //如果是手动提交，同时播放，则等到全部提交完毕再播放
        if (this.wallcontract.contractAnimation === 'SAME_MANUAL_SAMETIME') {
          if (this.needDrawSignList.length === this.deviceNumber) {
            this.playSign()
          }
        } else {
          this.playSign()
        }
      } catch (e) {
        console.log(e)
      }
    },
    /*签名播放,不论签名数据来自哪里，统统放入needDrawSignList,然后调用playSign进行播放，
    playSign会自动过滤掉已经播放过的签名
    如果完成一半的签名，在进入drawSignItem的时候会过滤
    */
    async playSign() {
      //fly为正常落款，noFly不落款，togetherFly一起落款
      let contractAnimation = this.wallcontract.contractAnimation
      if (flyList.includes(contractAnimation)) {
        this.needDrawSignList.forEach(item => {
          if (!this.completeFly[item.deviceId]) {
            //如果是备用签名且为图片
            let imgUrl
            if (item.spareSignatureType === 'STATIC_PICTURE') {
              imgUrl = item.spareSignaturePicture
            }
            this.drawSignItem(item, 'fly', imgUrl)
          }
        })
      }
      //noFly不落款
      if (noFlyList.includes(contractAnimation)) {
        this.needDrawSignList.forEach(item => {
          if (!this.completeDraw[item.deviceId]) {
            //如果是备用签名且为图片
            let imgUrl
            if (item.spareSignatureType === 'STATIC_PICTURE') {
              imgUrl = item.spareSignaturePicture
            }
            this.drawSignItem(item, 'noFly', imgUrl)
          }
        })
      }
      //一起落入的，需要等所有的画完，再落下，其他的要么不需要落款，要么draw就直接落了
      if (togetherFlyList.includes(contractAnimation)) {
        let doawPromoiseArr = []
        this.needDrawSignList.forEach(item => {
          //如果是备用签名且为图片
          let imgUrl
          if (item.spareSignatureType === 'STATIC_PICTURE') {
            imgUrl = item.spareSignaturePicture
          }
          doawPromoiseArr.push(this.drawSignItem(item, 'togetherFly', imgUrl))
        })
        await Promise.all(doawPromoiseArr)
        if (Object.values(this.completeDraw).filter(i => i).length === this.deviceNumber) {
          this.needDrawSignList.forEach(item => {
            this.signFlydown(item)
          })
        }
      }
    },
    //绘制单个设备签名,按照设备id作为一个签名，如果该设备对应对个签名，则一同绘制,
    //imgUrl为图片地址，如签名为图片时使用
    async drawSignItem(signItem, flyType, imgUrl) {
      //如果已经绘制过，则返回
      if (this.completeDraw[signItem.deviceId]) return
      //如果是非实时状态正在绘制的签名，则返回
      if (this.drawIng[signItem.deviceId]) return
      //如果实时，又不是确定提交的数据，正在绘制的，返回，
      if (this.drawIng[signItem.deviceId] && this.isOnLineSign && signItem.type === 'sameTime') return
      // 取出dom节点
      let deviceId = signItem.deviceId
      this.drawIng[deviceId] = signItem
      let canvasArr = this.$refs[`canvas_${deviceId}`]
      let drawArr = []
      // 取出设备的所有ID
      let posArr = this.signaturePositionList.filter(item => item.deviceId === deviceId)
      // 绘制签名
      canvasArr &&
        canvasArr.forEach(async canvas => {
          let tempArr = posArr.filter(item => item.id == canvas.dataset.id)
          let rect = tempArr[0] || {}
          drawArr.push(
            new Promise(async (resolve, reject) => {
              let ctx = canvas.getContext('2d')
              function clear() {
                ctx.clearRect(0, 0, canvas.width, canvas.height)
              }
              // 无论如何 开始的时候先清空画布
              clear()
              let showProcess = true //是否需要绘制过程显示
              //如果是实时签名，且不是备用前面，则不需要绘制过程显示
              if (this.isOnLineSign && !signItem.spareSignatureType) {
                showProcess = false
              }
              // 如果是图片，就画图，否则就画签名路径
              if (imgUrl) {
                let img = await Hi.Load.img(imgUrl)
                if (imgUrl.substr(-4, 4) === '.gif') {
                  //gif图要进行逐帧播放
                  await new Promise((r, j) => {
                    var sup1 = new SuperGif({
                      gif: img,
                      canvas,
                      lood_mode: false,
                      on_end: () => {
                        sup1.pause()
                        r()
                      },
                      show_progress_bar: false,
                      draw_while_loading: false,
                      max_width: this.canvasRect.width_big,
                    })
                    sup1.load(() => {
                      sup1.play()
                    })
                  })
                } else {
                  await ctx.drawImage(img, 0, 0, img.width, img.height, 0, 0, canvas.width, canvas.height)
                }
              } else {
                // 此情况下是需要按照完整的动画轨迹进行播放
                // 第一步  根据轨迹画图  等着画完
                let signaturePosition
                let stepPoints
                if (signItem.type === 'sameTime') {
                  stepPoints = signItem.signaturePosition.stepPoints
                } else {
                  signaturePosition = JSON.parse(signItem.signaturePosition)
                  let originWidth = signaturePosition.width // 根据大屏幕 缩放后 计算
                  stepPoints = Hi.Object.copy(signaturePosition.stepPoints)
                  let scale = this.canvasRect.width_big / originWidth || 1
                  stepPoints.map(item => {
                    item.map(_item => {
                      _item[0] = Math.floor(_item[0] * scale)
                      _item[1] = Math.floor(_item[1] * scale)
                      return _item
                    })
                    return item
                  })
                }
                await this.drawSignSteps(stepPoints, ctx, showProcess, canvas.dataset.id)
              }
              //如果不需要飞入
              if (['noFly', 'togetherFly'].includes(flyType)) {
                resolve('over')
              } else {
                //如果是非实时，或者实时的时候，签名已经完成，则飞入
                if (!this.isOnLineSign || (this.isOnLineSign && signItem.type !== 'sameTime')) {
                  this.drawIng[deviceId] = null
                  this.completeDraw[deviceId] = true
                  await this.signFlydown(signItem)
                }
                resolve('over')
              }
            }),
          )
        })
      await Promise.all(drawArr)
      this.drawIng[deviceId] = null
      //非实时笔画的或为备用签名是，绘制完就完成
      if (signItem.type !== 'sameTime' || signItem.spareSignatureType) {
        this.completeDraw[deviceId] = true
      }
      //查看是否所有签名都播放结束，然后播放庆祝动画
      if (this.checkCompleteAll()) {
        this.celebrateSuccess()
      }
    },
    //落款动画
    async signFlydown(signItem) {
      //如果已经绘飞入，则返回
      if (this.completeFly[signItem.deviceId]) return
      // 取出设备的所有ID
      let deviceId = signItem.deviceId
      let posArr = this.signaturePositionList.filter(item => item.deviceId === deviceId)
      let canvasArr = this.$refs[`canvas_${deviceId}`]
      let flyPromiseArr = []
      canvasArr &&
        canvasArr.forEach(async canvas => {
          let tempArr = posArr.filter(item => item.id == canvas.dataset.id)
          let rect = tempArr[0] || {}
          flyPromiseArr.push(
            new Promise(async (resolve, reject) => {
              if (this.flyPromiseRejectObj[deviceId]) {
                this.flyPromiseRejectObj[deviceId].push(reject)
              } else {
                this.flyPromiseRejectObj[deviceId] = [reject]
              }
              const fly = new Tween({
                left: rect.x0,
                top: rect.y0,
                width: this.canvasRect.width_big,
                height: this.canvasRect.height_big,
              })
                .to(
                  {
                    left: rect.x1,
                    top: rect.y1,
                    width: this.canvasRect.width_small,
                    height: this.canvasRect.height_small,
                  },
                  1500 * this.speed,
                )
                .onUpdate(v => {
                  canvas.style.width = v.width + 'px'
                  canvas.style.height = v.height + 'px'
                  canvas.style.top = v.top + 'px'
                  canvas.style.left = v.left + 'px'
                })
                .delay(1000)
                .onComplete(v => {
                  resolve('over')
                })
                .start()
            }),
          )
        })
      await Promise.all(flyPromiseArr)
      this.completeFly[deviceId] = true
      //查看是否所有签名都播放结束，然后播放庆祝动画
      if (this.checkCompleteAll()) {
        this.celebrateSuccess()
      }
    },
    /*
    stepPoints签名路径，是一个三维数组
    id是canvas的绑定id，用于记录设置定时器和 清楚定时器
    */
    async drawSignSteps(stepPoints, ctx, showProcess, id) {
      let timeInter = this.isOnLineSign ? 20 : this.speed * 20
      let len = stepPoints.length
      let i = 0
      while (i < len) {
        let line = stepPoints[i] // 取出当前第一条轨迹的二维数组的坐标
        ctx.beginPath()
        ctx.shadowBlur = 1 // 阴影大小
        ctx.shadowColor = this.wallcontract.screensignatureColor // 阴影颜色
        ctx.strokeStyle = this.wallcontract.screensignatureColor // 线条颜色
        ctx.lineWidth = this.wallcontract.fontThickness || 2 // 线条宽度啊
        ctx.lineCap = 'round' // 为了抗锯齿
        ctx.lineJoin = 'round'
        let index = 0 // 用来索引
        //如果是实时的，就直接绘制，不要过程
        if (!showProcess) {
          while (1) {
            let item = line[index]
            if (!item || !item.length) {
              break
            }
            let x = item[0],
              y = item[1]
            if (index === 0) {
              ctx.moveTo(x, y)
            } else {
              ctx.lineTo(x, y)
            }
            index++
          }
          ctx.stroke()
          // 一条一条线的画
        } else {
          await new Promise(resolve => {
            clearInterval(this.drawInter[id])
            this.drawInter[id] = setInterval(() => {
              let item = line[index]
              if (!item || !item.length) {
                clearInterval(this.drawInter[id])
                resolve()
                return
              }
              let x = item[0],
                y = item[1]
              if (index === 0) {
                ctx.moveTo(x, y)
              } else {
                ctx.lineTo(x, y)
              }
              ctx.stroke()
              index++
            }, timeInter)
          })
          // 递增去取就完了
        }
        i++
      }
    },
    //播放备用单个签名
    async playStandby(e, posItem) {
      //通过300ms延迟来避免双击事件触发单击事件
      clearTimeout(this.clickTime)
      this.clickTime = setTimeout(() => {
        this.dealStandbyItem(posItem)
      }, 300)
    },
    //播放备用签名方法
    async dealStandbyItem(posItem) {
      let deviceInfo = this.deviceObj[posItem.deviceId]
      let obj = {
        STATIC_PICTURE: 'spareSignaturePicture',
        DYNAMIC_HANDWRITING: 'spareSignatureHandwriting',
      }
      //如果没有备用签名，就返回
      if (!deviceInfo[obj[deviceInfo.spareSignatureType]]) return
      let signInfo = {
        deviceId: posItem.deviceId,
        signaturePosition: deviceInfo.spareSignatureHandwriting, //动态备用签名
        spareSignatureType: deviceInfo.spareSignatureType, //备用签名类型
        spareSignaturePicture: deviceInfo.spareSignaturePicture,
      }
      //将签名数据替换成备用签名数据
      let index = this.needDrawSignList.findIndex(i => i.deviceId === posItem.deviceId)
      //如果已经是备用签名了，那么就返回吧
      if (index > -1 && this.needDrawSignList[index].spareSignatureType) return
      //重置签名位置信息
      this.resetCanvas(posItem)
      if (index > -1) {
        this.needDrawSignList.splice(index, 1, signInfo)
      } else {
        this.needDrawSignList.push(signInfo)
      }
      //如果这个签名已经完成了，从完成completeFly移除
      this.completeDraw[posItem.deviceId] = false
      this.completeFly[posItem.deviceId] = false
      this.drawIng[posItem.deviceId] = null
      this.playSign()
    },
    //播放所有备用签名
    playAllStandby() {
      clearTimeout(this.clickTime)
      let temp = {}
      this.signaturePositionList.forEach(posItem => {
        if (!temp[posItem.deviceId]) {
          this.dealStandbyItem(posItem)
          temp[posItem.deviceId] = true
        }
      })
    },
    //清楚签名并还原该签名的初始信息
    resetCanvas(signItem) {
      let deviceId = signItem.deviceId
      let canvasArr = this.$refs[`canvas_${deviceId}`]
      let posArr = this.signaturePositionList.filter(item => item.deviceId === deviceId)
      canvasArr &&
        canvasArr.forEach(async canvas => {
          //清楚绘制定时器
          let id = canvas.dataset.id
          clearInterval(this.drawInter[id])
          let ctx = canvas.getContext('2d')
          ctx.clearRect(0, 0, canvas.width, canvas.height)

          let tempArr = posArr.filter(item => item.id == canvas.dataset.id)
          let rect = tempArr[0] || {}
          canvas.style.left = rect.x0 + 'px'
          canvas.style.top = rect.y0 + 'px'
          canvas.style.width = this.canvasRect.width_big + 'px'
          canvas.style.height = this.canvasRect.height_big + 'px'
        })
    },
    // 处理canvas 位置数组 和 父盒子位置和宽高
    async dealPostion() {
      try {
        // 查询当前轮次下 所有设备签名对应的位置信息
        let positionArr = await api.contractposition.list({
          where: {
            contractId: this.wallcontract.id,
          },
        })
        let arr = []
        //  根据窗口的大小动态计算盒子需要显示的大小
        positionArr.forEach(item => {
          let obj = {}
          let pos = JSON.parse(item.signaturePosition)
          obj.y0 = pos.y0 * this.scale
          obj.x0 = pos.x0 * this.scale
          if (this.wallcontract.contractType === 'DIFFERENT_LOCATION') {
            //不同位置
            obj.x1 = pos.x1 * this.scale
            obj.y1 = pos.y1 * this.scale
          }
          obj.deviceId = item.deviceId
          obj.id = item.id
          arr.push(obj)
        })
        // 这个用来渲染整个签名的 canvas 位置
        this.signaturePositionList = arr
        //将每个设备id对应的位置id存起来
        arr.forEach(i => {
          if (this.signDevicePosIdObj[i.deviceId]) {
            this.signDevicePosIdObj[i.deviceId].push(i.id)
          } else {
            this.signDevicePosIdObj[i.deviceId] = [i.id]
          }
        })
      } catch (e) {
        console.log(e)
      }
    },
    checkId(id) {
      let obj = JSON.parse(localStorage.getItem('contractHasPlayed') || '{}')
      return obj[id]
    },
    pushCache(id) {
      let obj = JSON.parse(localStorage.getItem('contractHasCelebrate') || '{}')
      obj[id] = true
      localStorage.setItem('contractHasCelebrate', JSON.stringify(obj))
    },
    // 截图
    async shotscreen() {
      try {
        // 弹窗显示的时候啥也不干 直接返回
        if (this.isShowTips) return
        // 如果正在进行中 提示 不让重复操作
        if (this.isShotting) {
          this.isShowTips = false
          this.saveState = 'isSaving'
          await this.$nextTick()
          this.isShowTips = true
          return
        }
        // 锁
        this.isShotting = true
        // 截图
        let canvas = await this.doScreenShot()
        var base64Data = canvas.toDataURL('image/jpeg', 1.0)
        //封装blob对象
        // eslint-disable-next-line no-inner-declarations
        function dataURItoBlob(base64Data) {
          var byteString
          if (base64Data.split(',')[0].indexOf('base64') >= 0) {
            byteString = atob(base64Data.split(',')[1])
          } else {
            byteString = unescape(base64Data.split(',')[1])
          }
          var mimeString = base64Data.split(',')[0].split(':')[1].split(';')[0]
          var ia = new Uint8Array(byteString.length)
          for (var i = 0; i < byteString.length; i++) {
            ia[i] = byteString.charCodeAt(i)
          }
          return new Blob([ia], { type: mimeString })
        }
        var blob = dataURItoBlob(base64Data)
        let data = await new Promise((resolve, reject) => {
          const xhr = new XMLHttpRequest()
          const action = this.$hi.url.upload
          const formData = new FormData()
          formData.append('file', blob)
          xhr.open('post', action, true)
          xhr.send(formData)
          xhr.onerror = function error(e) {
            reject(e)
          }
          function getBody(xhr) {
            const text = xhr.responseText || xhr.response
            if (!text) {
              return text
            }

            try {
              return JSON.parse(text)
            } catch (e) {
              return text
            }
          }
          xhr.onload = function onload() {
            if (xhr.status < 200 || xhr.status >= 300) {
              reject('上传失败')
              return
            }
            resolve(getBody(xhr))
          }
        })
        // 保存
        await api.contractrecord.save({
          contractImg: Hi.String.dealUrl(data.data.url),
        })
        // 显示弹窗
        this.saveState = 'isSaveDone'
        this.isShowTips = true
      } catch (e) {
        console.log(e)
      }
      // 解锁
      this.isShotting = false
    },
    async doScreenShot() {
      try {
        let canvas = await html2canvas(this.$refs.aniZone, {
          dpi: window.devicePixelRatio,
          //  backgroundColor: null,
          ignoreElements: true,
          useCORS: true,
        })
        return canvas
      } catch (e) {
        console.log(e)
      }
    },
    // 清空
    async clearContract() {
      try {
        await api.contractrecord.reset({
          where: {
            contractId: this.wallcontract.id,
          },
        })
      } catch (e) {
        console.log(e)
      }
    },
    // 轮次切换
    async switchActivity(dir = 'up') {
      if (this.isSwitched) return
      this.isSwitched = true
      await api.contract.switch({ wallFlag: this.wallFlag, type: dir })
      this.isSwitched = false
    },
    //成功后庆祝动作
    async celebrateSuccess() {
      //如果播放过庆祝动画，则不再播放
      //if(this.checkId(this.wallcontract.id)) return
      if (this.wallcontract.animationSwitch === 'N') return
      // this.pushCache(this.wallcontract.id) //保存播放动画的轮次
      let type = this.wallcontract.animationType || 'FIREWORKS'
      switch (type) {
        case 'FIREWORKS':
          //烟花
          this.showFireWork = true
          await timer(4000)
          this.showFireWork = false
          break
        case 'SPREAD_FLOWERS':
          //撒花
          for (let i = 0; i < 4; i++) {
            await timer(200)
            createFlower()
          }
          break
      }
    },
    // 查询签名设备列表
    async queryDevicecontract() {
      try {
        // 查询签名信息
        this.deviceList = await api.contractDevicecontract.list({
          where: {
            contarctId: this.wallcontract.id,
          },
        })
        this.deviceList.forEach(item => {
          this.deviceObj[item.deviceId] = item
        })
      } catch (e) {
        console.log(e)
      }
    },
    //缓存备用图片签名
    async loadStandySign() {
      try {
        //先判断是否设置了备用签名，如果没有数据，则不需要加载
        let hasStandy = this.deviceList.some(i => i.spareSignatureType)
        if (!hasStandy) return

        let promiseArr = []
        this.deviceList.forEach(item => {
          //缓存备用图片签名
          if (item.spareSignatureType === 'STATIC_PICTURE') {
            promiseArr.push(Hi.Load.img(item.spareSignaturePicture))
          }
        })
        await Promise.all(promiseArr)
      } catch (err) {
        console.log('备用签名加载失败！', err)
      }
    },
    //检查所有签名是否完成
    checkCompleteAll() {
      if (!this.deviceList.length) return false
      let contractAnimation = this.wallcontract.contractAnimation
      if (noFlyList.includes(contractAnimation)) {
        //不需要落款情况，所有的设备签名都播放结束就成功了
        return this.deviceList.every(i => this.completeDraw[i.deviceId])
      } else {
        //需要落款的情况，需要所有的设备签名落款完成
        return this.deviceList.every(i => this.completeFly[i.deviceId])
      }
    },
  },
  mounted() {
    this.handler = {
      'keyup-Space': this.shotscreen.bind(this),
      clearContract: this.clearContract.bind(this),
      'keyup-96': this.clearContract.bind(this), //重签快捷键 数字键盘的0
      'keyup-48': this.clearContract.bind(this), //重签快捷键
      'control-up-down': this.switchActivity.bind(this),
      updateSignature: this.querySignatureInfo.bind(this),
      reset: this.init.bind(this),
      doSyncWritingGo: this.doSyncWritingGo.bind(this),
    }
    //绑定快捷键
    window.addEventListener('resize', debounce(this.init.bind(this), 300))
  },
  async activated() {
    this.needDrawSignList = []
    await timer(500)
    this.renderStop = Hi.AnimateLoop.setup(v => {
      TWEEN.update()
    })
    if (this.wallcontract && this.wallcontract.id) {
      this.init()
    }
    Object.keys(this.handler).forEach(key => this.$bus.$on(key, this.handler[key]))
  },

  deactivated() {
    this.renderStop()
    Object.keys(this.handler).forEach(key => this.$bus.$off(key, this.handler[key]))
  },
}
</script>

<style lang="stylus" scoped>
.positionall
  position absolute
  top 0
  left 0
  width 100%
  height 100%
.stand
  cursor pointer
.contract-box
  z-index 1
  background #000
.bg
  z-index 1
  bottom 0
  right 0
  margin auto
  // height auto
  object-fit contain
  object-position center
.content
  z-index 2
.title
  position absolute
  top 0
  left 0
  text-align center
  width 100%
  height 100px
  font-size 42px
  line-height 100px
  color #fff
  z-index 10
.ani-zone
  width 1px
  height 1px
  position absolute
  top 0
  left 0
  right 0
  bottom 0
  margin auto
  z-index 1
  .cell
    position absolute
    z-index 5
    // background red
.tips
  position absolute
  top 0
  left 0
  right 0
  bottom 0
  margin auto
  z-index 100
  display inline-block
  width max-content
  height 84px
  line-height 86px
  padding 0 60px
  color #ffffff
  font-size 24px
  background-color rgba(0, 0, 0, 0.8)
  border-radius 8px
.fade-enter
.fade-leave-to
  opacity 0
  transform scale(0)
.fade-leave
.fade-enter-to
  opacity 1
  transform scale(1)
  transition all 0.3s
.flower
  width 100%
  height 100%
  position absolute
  left 0
  top 0
  z-index 10
.standby_img
  width 100%
  height 100%
  object-fit contain
</style>
