/*
 * @Author: crystal
 * @Date: 2021-03-23 13:54:35
 * @LastEditTime: 2021-03-30 16:27:50
 * @LastEditors: Please set LastEditors
 * @Description: 底部工具栏配置
 * @FilePath: \20210319\src\views\wall\control\config.js
 */
// 右侧固定操作按钮
const rightBtnList = [
  {
    id: 'music',
    name: '声音',
    keyName: 'shift_Y',
    keyCode: 89,
    tooltip: '屏幕音效开关,快捷键[shift+Y]',
    isBan: true, // 默认禁止
  },
  {
    id: 'danmu',
    name: '弹幕',
    keyName: 'D',
    keyCode: 68,
    tooltip: '屏幕弹幕开关，快捷键[D]',
    isBan: true, // 默认禁止
  },
  {
    id: 'moveqrcode',
    name: '二维码',
    keyName: 'M',
    keyCode: 77,
    tooltip: '屏幕二维码展示，快捷键[M]',
  },
  {
    id: 'fullscreen',
    name: '全屏',
    keyName: 'F11',
    keyCode: 10,
    tooltip: '全屏开关，快捷键[F11]',
    isBan: false,
  },
  {
    id: 'zhao',
    name: '自动切屏',
    keyName: '',
    keyCode: '',
    tooltip: '屏幕互动切换时，参与者手机自动切换至对应互动',
    isBan: true, // 默认禁止
  },
]
// 左侧所有互动按钮
const actList = [
  {
    id: 'topic',
    name: '主题',
    icon: 'wall_topic',
    color: '#499de2',
  },
  {
    id: 'msg',
    name: '消息',
    icon: 'wall_msg',
    color: '#499de2',
  },
  {
    id: 'question',
    name: '提问',
    icon: 'wall_question',
    color: '#41A1FF',
  },
  {
    id: 'wish',
    name: '许愿树',
    icon: 'wall_wish',
    color: '#499de2',
  },
  {
    id: 'signbook',
    name: '签到簿',
    icon: 'wall_signbook',
    color: '#499de2',
  },
  {
    id: 'signature',
    name: '签名墙',
    icon: 'wall_signature',
    color: '#41A1FF',
  },
  {
    id: 'pic',
    name: '图片',
    icon: 'wall_pic',
    color: '#41A1FF',
    tooltip: '图片墙',
  },
  ///////////////////
  {
    id: 'guest',
    name: '嘉宾',
    icon: 'wall_guest',
    color: '#2CCCD2',
  },
  {
    id: 'sign',
    name: '签到',
    icon: 'wall_sign',
    color: '#2ABE5B',
  },
  {
    id: 'threed',
    name: '3D墙',
    icon: 'wall_threed',
    color: '#2ABE5B',
  },
  {
    id: 'mapsign',
    name: '地图签到',
    icon: 'wall_mapsign',
    color: '#30B15A',
  },
  {
    id: 'countdown',
    name: '倒计时',
    icon: 'wall_countdown',
    color: '#2ABE5B',
  },
  {
    id: 'contract',
    name: '签约',
    icon: 'wall_contract',
    color: '#30B15A',
  },
  {
    id: 'launchingceremony',
    name: '启动仪式',
    icon: 'wall_launchingceremony',
    color: '#30B15A',
  },
  {
    id: 'performance',
    name: '业绩目标会',
    icon: 'wall_performance',
    color: '#30B15A',
  },
  {
    id: 'performancev3',
    name: '业绩目标会',
    icon: 'wall_performance',
    color: '#30B15A',
  },
  {
    id: 'placeorder',
    name: '订货会',
    icon: 'wall_placeorder',
    color: '#2ABE5B',
  },
  ///////////////////
  {
    id: 'lottery',
    name: '滚动抽奖',
    icon: 'wall_lottery',
    color: '#FF5B52',
  },
  {
    id: 'lotteryv3',
    name: '滚动抽奖',
    icon: 'wall_lottery',
    color: '#FF5B52',
  },
  {
    id: 'supperzzle',
    name: '对对碰',
    icon: 'wall_supperzzle',
    color: '#FF5B52',
  },
  {
    id: 'redpack',
    name: '红包雨',
    icon: 'wall_redpack',
    color: '#FF5B52',
    keyName: 'H',
  },
  {
    id: 'ropepack',
    name: '套红包',
    icon: 'wall_ropepack',
    color: '#FF5B52',
  },
  {
    id: 'voicepack',
    name: '语音红包',
    icon: 'wall_voicepack',
    color: '#FF5B52',
  },
  {
    id: 'piclottery',
    name: '图片抽奖',
    icon: 'wall_piclottery',
    color: '#FF5B52',
  },
  {
    id: 'piclotteryv3',
    name: '图片抽奖',
    icon: 'wall_piclottery',
    color: '#FF5B52',
  },

  {
    id: 'danmulottery',
    name: '弹幕抽奖',
    icon: 'wall_danmulottery',
    color: '#FF5B52',
  },
  {
    id: 'mysterybox',
    name: '拆盲盒',
    icon: 'wall_mysterybox',
    color: '#ff5b52',
  },
  {
    id: 'packetwall',
    name: '红包墙',
    icon: 'wall_packetwall',
    color: '#ff5b52',
  },
  {
    id: 'wheelsurf',
    name: '大转盘',
    icon: 'wall_topic',
    color: '#ff5b52',
  },
  {
    id: 'ninegrids',
    name: '九宫格',
    icon: 'wall_topic',
    color: '#ff5b52',
  },
  {
    id: 'listlottery',
    name: '名单抽奖',
    icon: 'wall_listlottery',
    color: '#ff5b52',
  },
  {
    id: 'listlotteryv3',
    name: '名单抽奖',
    icon: 'wall_listlottery',
    color: '#ff5b52',
  },
  {
    id: 'seglottery',
    name: '排座抽奖',
    icon: 'wall_seglottery',
    color: '#ff5b52',
  },
  {
    id: 'photolottery',
    name: '拍照抽奖',
    icon: 'wall_photolottery',
    color: '#ff5b52',
  },
  ///////////////////
  {
    id: 'shakev3',
    name: '摇一摇',
    icon: 'wall_shake',
    color: '#FBBA1E',
  },
  {
    id: 'diglettv3',
    name: '敲敲乐',
    icon: 'wall_diglett',
    color: '#FBBA1E',
  },
  {
    id: 'moneyv3',
    name: '数钱',
    icon: 'wall_money',
    color: '#FBBA1E',
  },
  {
    id: 'tugwar',
    name: '拔河',
    icon: 'wall_tugwar',
    color: '#FBBA1E',
  },
  {
    id: 'shoot',
    name: '最佳射手',
    icon: 'wall_shoot',
    color: '#FBBA1E',
  },
  {
    id: 'firev3',
    name: '射击游戏',
    icon: 'wall_fire',
    color: '#FBBA1C',
  },
  {
    id: 'goldcoinv3',
    name: '接金币',
    icon: 'wall_goldcoin',
    color: '#FFBA1F',
  },
  {
    id: 'answerrace',
    name: '答题',
    icon: 'wall_answerrace',
    color: '#FBBA1E',
  },
  {
    id: 'answerracev3',
    name: '答题',
    icon: 'wall_answerrace',
    color: '#FBBA1E',
  },
  {
    id: 'teamanswer',
    name: '团队答题',
    icon: 'wall_teamanswer',
    color: '#FBBA1E',
  },
  {
    id: 'playguess',
    name: '你演我猜',
    icon: 'wall_playguess',
    color: '#FBBA1E',
  },
  ///////////////////
  {
    id: 'vote',
    name: '投票',
    icon: 'wall_vote',
    color: '#775FFF',
  },
  {
    id: 'reward',
    name: '打赏',
    icon: 'wall_reward',
    color: '#775FFF',
  },
  {
    id: 'mark',
    name: '评分',
    icon: 'wall_mark',
    color: '#775FFF',
  },
]
// 定义不同互动的中间操作按钮
const centerObj = {
  msg: [
    {
      id: 'first',
      name: '首页',
      key: 'keyup-ArrowLeft',
      tooltip: '快捷键(←)',
    },
    {
      id: 'up',
      name: '上一页',
      key: 'keyup-ArrowUp',
      tooltip: '快捷键(↑)',
    },
    {
      id: 'space',
      name: '播放',
      key: 'keyup-Space',
      tooltip: '快捷键(空格)',
    },
    {
      id: 'down',
      name: '下一页',
      key: 'keyup-ArrowDown',
      tooltip: '快捷键(↓)',
    },
    {
      id: 'last',
      name: '末页',
      key: 'keyup-ArrowRight',
      tooltip: '快捷键(→)',
    },
  ],
  pic: [
    {
      id: 'first',
      name: '首页',
      key: 'keyup-ArrowLeft',
      tooltip: '快捷键(←)',
    },
    {
      id: 'up',
      name: '上一页',
      key: 'keyup-ArrowUp',
      tooltip: '快捷键(↑)',
    },
    {
      id: 'space',
      name: '播放',
      key: 'keyup-Space',
      tooltip: '快捷键(空格)',
    },
    {
      id: 'down',
      name: '下一页',
      key: 'keyup-ArrowDown',
      tooltip: '快捷键(↓)',
    },
    {
      id: 'last',
      name: '末页',
      key: 'keyup-ArrowRight',
      tooltip: '快捷键(→)',
    },
  ],
  question: [
    {
      id: 'up',
      name: '上',
      key: 'keyup-ArrowUp',
      tooltip: '快捷键(↑)',
    },
    {
      id: 'down',
      name: '下',
      key: 'keyup-ArrowDown',
      tooltip: '快捷键(↓)',
    },
    {
      id: 'left',
      name: '左',
      key: 'keyup-ArrowLeft',
      tooltip: '快捷键(←)',
    },
    {
      id: 'right',
      name: '右',
      key: 'keyup-ArrowRight',
      tooltip: '快捷键(→)',
    },
    {
      id: 'space',
      name: '查看/关闭',
      key: 'keyup-Space',
      tooltip: '快捷键(空格)',
    },
  ],
  contract: [
    {
      id: 'up',
      name: '上一轮',
      key: 'keyup-ArrowUp',
      tooltip: '快捷键(↑)',
    },
    {
      id: 'down',
      name: '下一轮',
      key: 'keyup-ArrowDown',
      tooltip: '快捷键(↓)',
    },
    {
      id: 'space',
      name: '保存',
      key: 'keyup-Space',
      tooltip: '快捷键(空格)',
    },
  ],
  guest: [
    {
      id: 'up',
      name: '上一个',
      key: 'keyup-ArrowUp',
      tooltip: '快捷键(↑)',
    },
    {
      id: 'down',
      name: '下一个',
      key: 'keyup-ArrowDown',
      tooltip: '快捷键(↓)',
    },
    {
      id: 'space',
      name: '放大/缩小',
      key: 'keyup-Space',
      tooltip: '快捷键(空格)',
    },
  ],
  launchingceremony: [
    {
      id: 'space',
      name: '开始',
      key: 'keyup-Space',
      tooltip: '快捷键(空格)',
    },
  ],
  countdown: [
    {
      id: 'space',
      name: '开始',
      key: 'keyup-Space',
      tooltip: '快捷键(空格)',
    },
  ],
  lottery: [
    {
      id: 'up',
      name: '上一轮',
      key: 'keyup-ArrowUp',
      tooltip: '快捷键(↑)',
    },
    {
      id: 'down',
      name: '下一轮',
      key: 'keyup-ArrowDown',
      tooltip: '快捷键(↓)',
    },
    {
      id: 'left',
      img: 'jian',
      name: '减人',
      key: 'keyup-ArrowLeft',
      tooltip: '快捷键(←)',
    },
    {
      id: 'right',
      img: 'jia',
      name: '加人',
      key: 'keyup-ArrowRight',
      tooltip: '快捷键(→)',
    },
  ],
  lotteryv3: [
    {
      id: 'up',
      name: '上一轮',
      key: 'keyup-ArrowUp',
      tooltip: '快捷键(↑)',
    },
    {
      id: 'down',
      name: '下一轮',
      key: 'keyup-ArrowDown',
      tooltip: '快捷键(↓)',
    },
    {
      id: 'left',
      img: 'jian',
      name: '减人',
      key: 'keyup-ArrowLeft',
      tooltip: '快捷键(←)',
    },
    {
      id: 'right',
      img: 'jia',
      name: '加人',
      key: 'keyup-ArrowRight',
      tooltip: '快捷键(→)',
    },
  ],
  listlottery: [
    {
      id: 'up',
      name: '上一轮',
      key: 'keyup-ArrowUp',
      tooltip: '快捷键(↑)',
    },
    {
      id: 'down',
      name: '下一轮',
      key: 'keyup-ArrowDown',
      tooltip: '快捷键(↓)',
    },
    {
      id: 'left',
      img: 'jian',
      name: '减人',
      key: 'keyup-ArrowLeft',
      tooltip: '快捷键(←)',
    },
    {
      id: 'right',
      img: 'jia',
      name: '加人',
      key: 'keyup-ArrowRight',
      tooltip: '快捷键(→)',
    },
  ],
  listlotteryv3: [
    {
      id: 'up',
      name: '上一轮',
      key: 'keyup-ArrowUp',
      tooltip: '快捷键(↑)',
    },
    {
      id: 'down',
      name: '下一轮',
      key: 'keyup-ArrowDown',
      tooltip: '快捷键(↓)',
    },
    {
      id: 'left',
      img: 'jian',
      name: '减人',
      key: 'keyup-ArrowLeft',
      tooltip: '快捷键(←)',
    },
    {
      id: 'right',
      img: 'jia',
      name: '加人',
      key: 'keyup-ArrowRight',
      tooltip: '快捷键(→)',
    },
  ],
  seglottery: [
    {
      id: 'up',
      name: '上一轮',
      key: 'keyup-ArrowUp',
      tooltip: '快捷键(↑)',
    },
    {
      id: 'down',
      name: '下一轮',
      key: 'keyup-ArrowDown',
      tooltip: '快捷键(↓)',
    },
  ],
  danmulottery: [
    {
      id: 'up',
      name: '上一轮',
      key: 'keyup-ArrowUp',
      tooltip: '快捷键(↑)',
    },
    {
      id: 'down',
      name: '下一轮',
      key: 'keyup-ArrowDown',
      tooltip: '快捷键(↓)',
    },
    {
      id: 'left',
      img: 'jian',
      name: '减少',
      key: 'keyup-ArrowLeft',
      tooltip: '快捷键(←)',
    },
    {
      id: 'right',
      img: 'jia',
      name: '增加',
      key: 'keyup-ArrowRight',
      tooltip: '快捷键(→)',
    },
  ],
  wheelsurf: [
    {
      id: 'up',
      name: '上一轮',
      key: 'keyup-ArrowUp',
      tooltip: '快捷键(↑)',
    },
    {
      id: 'down',
      name: '下一轮',
      key: 'keyup-ArrowDown',
      tooltip: '快捷键(↓)',
    },
    {
      id: 'space',
      name: '开始',
      key: 'keyup-Space',
      tooltip: '快捷键(空格)',
    },
  ],
  ninegrids: [
    {
      id: 'up',
      name: '上一轮',
      key: 'keyup-ArrowUp',
      tooltip: '快捷键(↑)',
    },
    {
      id: 'down',
      name: '下一轮',
      key: 'keyup-ArrowDown',
      tooltip: '快捷键(↓)',
    },
    {
      id: 'space',
      name: '开始',
      key: 'keyup-Space',
      tooltip: '快捷键(空格)',
    },
  ],
  redpack: [
    {
      id: 'up',
      name: '上一轮',
      key: 'keyup-ArrowUp',
      tooltip: '快捷键(↑)',
    },
    {
      id: 'down',
      name: '下一轮',
      key: 'keyup-ArrowDown',
      tooltip: '快捷键(↓)',
    },
  ],
  ropepack: [
    {
      id: 'up',
      name: '上一轮',
      key: 'keyup-ArrowUp',
      tooltip: '快捷键(↑)',
    },
    {
      id: 'down',
      name: '下一轮',
      key: 'keyup-ArrowDown',
      tooltip: '快捷键(↓)',
    },
  ],
  voicepack: [
    {
      id: 'up',
      name: '上一轮',
      key: 'keyup-ArrowUp',
      tooltip: '快捷键(↑)',
    },
    {
      id: 'down',
      name: '下一轮',
      key: 'keyup-ArrowDown',
      tooltip: '快捷键(↓)',
    },
  ],
  piclottery: [
    {
      id: 'up',
      name: '上一轮',
      key: 'keyup-ArrowUp',
      tooltip: '快捷键(↑)',
    },
    {
      id: 'down',
      name: '下一轮',
      key: 'keyup-ArrowDown',
      tooltip: '快捷键(↓)',
    },
    {
      id: 'left',
      img: 'jian',
      name: '减少',
      key: 'keyup-ArrowLeft',
      tooltip: '快捷键(←)',
    },
    {
      id: 'right',
      img: 'jia',
      name: '增加',
      key: 'keyup-ArrowRight',
      tooltip: '快捷键(→)',
    },
  ],
  piclotteryv3: [
    {
      id: 'up',
      name: '上一轮',
      key: 'keyup-ArrowUp',
      tooltip: '快捷键(↑)',
    },
    {
      id: 'down',
      name: '下一轮',
      key: 'keyup-ArrowDown',
      tooltip: '快捷键(↓)',
    },
    {
      id: 'left',
      img: 'jian',
      name: '减少',
      key: 'keyup-ArrowLeft',
      tooltip: '快捷键(←)',
    },
    {
      id: 'right',
      img: 'jia',
      name: '增加',
      key: 'keyup-ArrowRight',
      tooltip: '快捷键(→)',
    },
  ],
  photolottery: [
    {
      id: 'up',
      name: '上一轮',
      key: 'keyup-ArrowUp',
      tooltip: '快捷键(↑)',
    },
    {
      id: 'down',
      name: '下一轮',
      key: 'keyup-ArrowDown',
      tooltip: '快捷键(↓)',
    },
    {
      id: 'left',
      img: 'jian',
      name: '减少',
      key: 'keyup-ArrowLeft',
      tooltip: '快捷键(←)',
    },
    {
      id: 'right',
      img: 'jia',
      name: '增加',
      key: 'keyup-ArrowRight',
      tooltip: '快捷键(→)',
    },
  ],
  shake: [
    {
      id: 'up',
      name: '上一轮',
      key: 'keyup-ArrowUp',
      tooltip: '快捷键(↑)',
    },
    {
      id: 'down',
      name: '下一轮',
      key: 'keyup-ArrowDown',
      tooltip: '快捷键(↓)',
    },
  ],
  shakev3: [
    {
      id: 'up',
      name: '上一轮',
      key: 'keyup-ArrowUp',
      tooltip: '快捷键(↑)',
    },
    {
      id: 'down',
      name: '下一轮',
      key: 'keyup-ArrowDown',
      tooltip: '快捷键(↓)',
    },
  ],
  diglett: [
    {
      id: 'up',
      name: '上一轮',
      key: 'keyup-ArrowUp',
      tooltip: '快捷键(↑)',
    },
    {
      id: 'down',
      name: '下一轮',
      key: 'keyup-ArrowDown',
      tooltip: '快捷键(↓)',
    },
  ],
  diglettv3: [
    {
      id: 'up',
      name: '上一轮',
      key: 'keyup-ArrowUp',
      tooltip: '快捷键(↑)',
    },
    {
      id: 'down',
      name: '下一轮',
      key: 'keyup-ArrowDown',
      tooltip: '快捷键(↓)',
    },
  ],
  money: [
    {
      id: 'up',
      name: '上一轮',
      key: 'keyup-ArrowUp',
      tooltip: '快捷键(↑)',
    },
    {
      id: 'down',
      name: '下一轮',
      key: 'keyup-ArrowDown',
      tooltip: '快捷键(↓)',
    },
  ],
  moneyv3: [
    {
      id: 'up',
      name: '上一轮',
      key: 'keyup-ArrowUp',
      tooltip: '快捷键(↑)',
    },
    {
      id: 'down',
      name: '下一轮',
      key: 'keyup-ArrowDown',
      tooltip: '快捷键(↓)',
    },
  ],
  tugwar: [
    {
      id: 'up',
      name: '上一轮',
      key: 'keyup-ArrowUp',
      tooltip: '快捷键(↑)',
    },
    {
      id: 'down',
      name: '下一轮',
      key: 'keyup-ArrowDown',
      tooltip: '快捷键(↓)',
    },
  ],
  shoot: [
    {
      id: 'up',
      name: '上一轮',
      key: 'keyup-ArrowUp',
      tooltip: '快捷键(↑)',
    },
    {
      id: 'down',
      name: '下一轮',
      key: 'keyup-ArrowDown',
      tooltip: '快捷键(↓)',
    },
  ],
  answerrace: [
    {
      id: 'up',
      name: '上一轮',
      key: 'keyup-ArrowUp',
      tooltip: '快捷键(↑)',
    },
    {
      id: 'down',
      name: '下一轮',
      key: 'keyup-ArrowDown',
      tooltip: '快捷键(↓)',
    },
    {
      id: 'space',
      name: '查看注释',
      img: 'zhushi',
      key: 'keyup-Space',
      tooltip: '快捷键(空格)',
    },
    {
      id: 'left',
      name: '结束',
      key: 'keyup-ArrowLeft',
      tooltip: '快捷键(←)',
    },
    {
      id: 'right',
      name: '下一题',
      key: 'keyup-ArrowRight',
      tooltip: '快捷键(→)',
    },
  ],
  answerracev3: [
    {
      id: 'up',
      name: '上一轮',
      key: 'keyup-ArrowUp',
      tooltip: '快捷键(↑)',
    },
    {
      id: 'down',
      name: '下一轮',
      key: 'keyup-ArrowDown',
      tooltip: '快捷键(↓)',
    },
    // {
    //   id: 'space',
    //   name: '查看注释',
    //   img: 'zhushi',
    //   key: 'keyup-Space',
    //   tooltip: '快捷键(空格)',
    // },
    // {
    //   id: 'left',
    //   name: '结束',
    //   key: 'keyup-ArrowLeft',
    //   tooltip: '快捷键(←)',
    // },
    // {
    //   id: 'right',
    //   name: '下一题',
    //   key: 'keyup-ArrowRight',
    //   tooltip: '快捷键(→)',
    // },
  ],
  teamanswer: [
    {
      id: 'up',
      name: '首页',
      img: 'up',
      key: 'keyup-ArrowUp',
      tooltip: '快捷键(↑)',
    },
    {
      id: 'down',
      name: '计分板',
      img: 'rank',
      key: 'keyup-ArrowDown',
      tooltip: '快捷键(↓)',
    },
    {
      id: 'left',
      name: '上一个',
      img: 'left',
      key: 'keyup-ArrowLeft',
      tooltip: '快捷键(←)',
    },
    {
      id: 'right',
      name: '下一个',
      img: 'right',
      key: 'keyup-ArrowRight',
      tooltip: '快捷键(→)',
    },
    {
      id: 'space',
      name: '开始',
      img: 'play',
      key: 'keyup-Space',
      tooltip: '快捷键(空格)',
    },
  ],
  playguess: [
    {
      id: 'up',
      name: '上一轮',
      key: 'keyup-ArrowUp',
      tooltip: '快捷键(↑)',
    },
    {
      id: 'down',
      name: '下一轮',
      key: 'keyup-ArrowDown',
      tooltip: '快捷键(↓)',
    },
    {
      id: 'left',
      name: '上一个',
      key: 'keyup-ArrowLeft',
      tooltip: '快捷键(←)',
    },
    {
      id: 'right',
      name: '下一个',
      key: 'keyup-ArrowRight',
      tooltip: '快捷键(→)',
    },
    {
      id: 'space',
      name: '开始',
      key: 'keyup-Space',
      tooltip: '快捷键(空格)',
    },
  ],
  vote: [
    {
      id: 'up',
      name: '上一轮',
      key: 'keyup-ArrowUp',
      tooltip: '快捷键(↑)',
    },
    {
      id: 'down',
      name: '下一轮',
      key: 'keyup-ArrowDown',
      tooltip: '快捷键(↓)',
    },
    {
      id: 'space',
      name: '查看注释',
      key: 'keyup-Space',
      tooltip: '快捷键(空格)',
    },
    {
      id: 'left',
      name: '上一位',
      key: 'keyup-ArrowLeft',
      tooltip: '快捷键(←)',
    },
    {
      id: 'right',
      name: '下一位',
      key: 'keyup-ArrowRight',
      tooltip: '快捷键(→)',
    },
  ],
  reward: [
    {
      id: 'up',
      name: '上一轮',
      key: 'keyup-ArrowUp',
      tooltip: '快捷键(↑)',
    },
    {
      id: 'down',
      name: '下一轮',
      key: 'keyup-ArrowDown',
      tooltip: '快捷键(↓)',
    },
    {
      id: 'left',
      name: '上一位',
      key: 'keyup-ArrowLeft',
      tooltip: '快捷键(←)',
    },
    {
      id: 'right',
      name: '下一位',
      key: 'keyup-ArrowRight',
      tooltip: '快捷键(→)',
    },
  ],
  mark: [
    {
      id: 'up',
      name: '上一轮',
      key: 'keyup-ArrowUp',
      tooltip: '快捷键(↑)',
    },
    {
      id: 'down',
      name: '下一轮',
      key: 'keyup-ArrowDown',
      tooltip: '快捷键(↓)',
    },
    {
      id: 'space',
      name: '开始',
      key: 'keyup-Space',
      tooltip: '快捷键(空格)',
    },
    {
      id: 'left',
      name: '上一个',
      key: 'keyup-ArrowLeft',
      tooltip: '快捷键(←)',
    },
    {
      id: 'right',
      name: '下一个',
      key: 'keyup-ArrowRight',
      tooltip: '快捷键(→)',
    },
  ],
  placeorder: [
    {
      id: 'up',
      name: '上一轮',
      key: 'keyup-ArrowUp',
      tooltip: '快捷键(↑)',
    },
    {
      id: 'space',
      name: '开始',
      key: 'keyup-Space',
      tooltip: '快捷键(空格)',
    },
    {
      id: 'down',
      name: '下一轮',
      key: 'keyup-ArrowDown',
      tooltip: '快捷键(↓)',
    },
  ],
  performance: [
    {
      id: 'up',
      name: '上一轮',
      key: 'keyup-ArrowUp',
      tooltip: '快捷键(↑)',
    },
    {
      id: 'space',
      name: '开始',
      key: 'keyup-Space',
      tooltip: '快捷键(空格)',
    },
    {
      id: 'down',
      name: '下一轮',
      key: 'keyup-ArrowDown',
      tooltip: '快捷键(↓)',
    },
  ],
  blindbox: [
    {
      id: 'up',
      name: '上一页',
      key: 'keyup-ArrowUp',
      tooltip: '快捷键(↑)',
    },
    {
      id: 'space',
      name: '开始',
      key: 'keyup-Space',
      tooltip: '快捷键(空格)',
    },
    {
      id: 'down',
      name: '下一页',
      key: 'keyup-ArrowDown',
      tooltip: '快捷键(↓)',
    },
  ],
  mysterybox: [
    {
      id: 'up',
      name: '上一页',
      key: 'keyup-ArrowUp',
      tooltip: '快捷键(↑)',
    },
    {
      id: 'space',
      name: '开始',
      key: 'keyup-Space',
      tooltip: '快捷键(空格)',
    },
    {
      id: 'down',
      name: '下一页',
      key: 'keyup-ArrowDown',
      tooltip: '快捷键(↓)',
    },
  ],
  packetwall: [
    {
      id: 'up',
      name: '上一页',
      key: 'keyup-ArrowUp',
      tooltip: '快捷键(↑)',
    },
    {
      id: 'space',
      name: '开始',
      key: 'keyup-Space',
      tooltip: '快捷键(空格)',
    },
    {
      id: 'down',
      name: '下一页',
      key: 'keyup-ArrowDown',
      tooltip: '快捷键(↓)',
    },
  ],
  fire: [
    {
      id: 'up',
      name: '上一轮',
      key: 'keyup-ArrowUp',
      tooltip: '快捷键(↑)',
    },
    {
      id: 'down',
      name: '下一轮',
      key: 'keyup-ArrowDown',
      tooltip: '快捷键(↓)',
    },
  ],
  firev3: [
    {
      id: 'up',
      name: '上一轮',
      key: 'keyup-ArrowUp',
      tooltip: '快捷键(↑)',
    },
    {
      id: 'down',
      name: '下一轮',
      key: 'keyup-ArrowDown',
      tooltip: '快捷键(↓)',
    },
  ],
  goldcoin: [
    {
      id: 'up',
      name: '上一轮',
      key: 'keyup-ArrowUp',
      tooltip: '快捷键(↑)',
    },
    {
      id: 'down',
      name: '下一轮',
      key: 'keyup-ArrowDown',
      tooltip: '快捷键(↓)',
    },
  ],
  goldcoinv3: [
    {
      id: 'up',
      name: '上一轮',
      key: 'keyup-ArrowUp',
      tooltip: '快捷键(↑)',
    },
    {
      id: 'down',
      name: '下一轮',
      key: 'keyup-ArrowDown',
      tooltip: '快捷键(↓)',
    },
  ],
  supperzzle: [
    {
      id: 'up',
      name: '上一轮',
      key: 'keyup-ArrowUp',
      tooltip: '快捷键(↑)',
    },
    {
      id: 'down',
      name: '下一轮',
      key: 'keyup-ArrowDown',
      tooltip: '快捷键(↓)',
    },
  ],
  wish: [
    {
      id: 'space',
      name: '开始',
      key: 'keyup-Space',
      tooltip: '快捷键(空格)',
    },
  ],
}
export { rightBtnList, actList, centerObj }
