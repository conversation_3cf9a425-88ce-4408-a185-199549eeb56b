export const themeConfig = {
  DEFAULT: {
    startImg: require('./img/default/start.png'),
    stopImg: require('./img/default/stop.png'),
    purpleStyle: {
      backgroundImage: `url(${require('./img/default/botbg.png')})`,
    },
    jiaImg: require('./img/default/jia.png'),
    jianImg: require('./img/default/jian.png'),
    winImg: require('./img/default/win.png'),
    rankTitleImg: require('./img/default/tit.png'),
    rankCloseImg: require('./img/default/close.png'),
    rankItemHeadStyle: {
      border: '2px solid #7D48D8',
      borderRadius: '50%',
    },
    rankItemZsImg: require('./img/default/itemzs.png'),
    rankItemStyle: {
      background: '#fff',
    },
  },
  KEHUAN: {
    startImg: require('./img/kehuan/start.png'),
    stopImg: require('./img/kehuan/stop.png'),
    purpleStyle: {
      backgroundImage: `url(${require('./img/kehuan/botbg.png')})`,
      color: '#111',
      padding: '0 4px',
    },
    jiaImg: require('./img/kehuan/jia.png'),
    jianImg: require('./img/kehuan/jian.png'),
    winImg: require('./img/kehuan/win.png'),
    awardStyle: {
      background: `url(${require('./img/kehuan/prizebg.png')}) no-repeat`,
      padding: '35px 35px',
    },
    awardCoverImg: require('./img/kehuan/prizebg.png'),
    rankTitleImg: require('./img/kehuan/tit.png'),
    rankStyle: {
      background: 'rgba(9,1,30,0.4)',
      border: '3px solid #9eff66',
    },
    rankItemStyle: {
      backgroundImage: `url(${require('./img/kehuan/rankitem.png')})`,
      color: '#fff',
      border: 'none',
    },
    rankItemHeadStyle: {
      border: 'none',
      padding: '5px',
      borderRadius: 'none',
    },
    rankTitleCoverImg: require('./img/kehuan/rankhead.png'),
    rankCloseImg: require('./img/kehuan/close.png'),
  },
}
