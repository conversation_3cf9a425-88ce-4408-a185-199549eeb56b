import Vue from 'vue'
import Router from 'vue-router'

Vue.use(Router)
// r => require.ensure([], () => r(require('@/views/test/test')), 'test')

let router = new Router({
  mode: 'hash',
  base: '/pro/pcwall/',
  routes: [
    {
      path: '/',
      redirect: '/wall.html',
    },
    {
      name: 'wall',
      path: '/wall.html',
      component: r => import(/* webpackChunkName: "wall" */ '@/views/wall'),
      meta: { title: '大屏幕' },
    },
    {
      name: 'nohaswall',
      path: '/nohaswall.html',
      component: r => import(/* webpackChunkName: "wall" */ '@/views/nohaswall'),
      meta: { title: '大屏幕提示' },
    },
    {
      name: 'msgsync',
      path: '/msgsync.html',
      component: r => import(/* webpackChunkName: "test" */ '@/views/msgsync'),
      meta: { title: '大屏幕' },
    },
    {
      name: 'test',
      path: '/test.html',
      component: r => import(/* webpackChunkName: "test" */ '@/views/test'),
      meta: { title: '大屏幕' },
    },
    // {
    //   name: 'wordcloudtest',
    //   path: '/wordcloudtest.html',
    //   component: r => import(/* webpackChunkName: "test" */ '@/views/wordcloudtest'),
    //   meta: { title: '大屏幕' },
    // },
    // {
    //   name: 'checkpwd',
    //   path: '/checkpwd.html',
    //   component: r => require.ensure([], () => r(require('@/views/checkpwd')), 'test'),
    //   meta: {
    //     title: '大屏幕'
    //   },
    // },
    {
      path: '*', // 匹配不到后的默认地址
      redirect: '/nohaswall.html',
    },
  ],
})
// 路由统一拦截设置页面title
router.beforeEach(async (to, from, next) => {
  // document.title = to.meta && to.meta.title ? to.meta.title : 'Hi现场'
  let wallFlag = to.query.wallFlag
  if (wallFlag) {
    await router.app.$store.dispatch('wall/initWall', wallFlag, { root: true })
    if (!router.app.$store.state.wall.wallFlag) {
      next({ name: 'nohaswall' })
    }
    // } else if (!['wallinfo', 'nohaswall', 'checkpwd'].includes(to.name)) {
  } else if (!['wallinfo', 'nohaswall'].includes(to.name)) {
    next({ name: 'wallinfo' })
    return
  }
  next()
  console.log('88177----',location.pathname)
})
export default router
