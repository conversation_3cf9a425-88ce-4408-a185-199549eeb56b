import api from './api'

export default {
  read: postData => api.fetchBaseData('/pro/hxc/web/prowall/read.htm', postData),
  // 查询所有活动配置信息
  allConfig: postData => api.fetchBaseData('/pro/hxc/web/prowall/allConfig.htm', postData),
  auth: postData => api.fetchBaseData('/pro/hxc/web/prowall/auth.htm', postData),
  commonqrcode: postData => api.fetchBaseData('/pro/hxc/web/prowall/commonqrcode.htm', postData),
  appidread: postData => api.fetchBaseData('/pro/hxc/prowallappid/read.htm', postData),
}
