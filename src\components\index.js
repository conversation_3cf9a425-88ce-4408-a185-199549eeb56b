import Vue from 'vue'

import './directive' // 自定义指令实现
import '@/components/tip/tip'

// // 自定义组件
// import HiAni from '@/components/animation'
// import HiEmoji from '@/components/emoji'
// import HiImageBox from '@/components/image-box'
// import HiImg from '@/components/img'
// import HiCountDown from '@/components/count-down'
// import HiColorpicker from '@/components/colorpicker'
// import HiAutoFontsize from '@/components/auto-fontsize'
// import HiBgVideo from '@/components/bg-video'
// import HiReelList from '@/components/reel-list'
// Vue.use(HiAni)
// Vue.use(HiEmoji)
// Vue.use(HiImageBox)
// Vue.use(HiImg)
// Vue.use(HiCountDown)
// Vue.use(HiUe)
// Vue.use(HiColorpicker)
// Vue.use(HiAutoFontsize)
// Vue.use(HiBgVideo)
// Vue.use(HiReelList)

const noLoadModule = {
  HiUe: true,
}

let requireAllModules = require.context('./', true, /\.js$/)
requireAllModules.keys().forEach(item => {
  let module = requireAllModules(item).default
  if (module && module.name && module.install && typeof module.install === 'function' && !noLoadModule[module.name]) {
    Vue.use(module)
  }
})
