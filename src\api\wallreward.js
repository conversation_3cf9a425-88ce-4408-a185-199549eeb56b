import api from './api'

export default {
  // 当前轮活动信息
  read: postData => api.fetchBaseData('/pro/hxc/web/prowallreward/read.htm', postData),
  // 打赏对象列表查询
  optionList: postData => api.fetchBaseData('/pro/hxc/web/prowallreward/optionList.htm', postData),
  // 打赏贡献榜
  recordList: postData => api.fetchBaseData('/pro/hxc/web/prowallreward/recordList.htm', postData),
  // 轮次切换
  switch: postData => api.fetchBaseData('/pro/hxc/web/prowallreward/switch.htm', postData),
}
